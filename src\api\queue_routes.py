"""
Task Queue Management API endpoints
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel

from src.services.task_queue import task_queue, TaskPriority, resource_monitor, concurrency_manager
from src.middleware.auth_middleware import require_auth, require_admin
from src.utils.logging import get_logger

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/queue", tags=["queue-management"])


# ============================================================================
# PYDANTIC SCHEMAS
# ============================================================================

class QueueStatusResponse(BaseModel):
    """Response schema for queue status"""
    queue_size: int
    retry_queue_size: int
    running_tasks: int
    max_concurrent: int
    available_slots: int
    is_running: bool


class UserQueueStatusResponse(BaseModel):
    """Response schema for user queue status"""
    user_id: str
    running_tasks: int
    queued_tasks: int
    total_active: int


class TaskPriorityUpdate(BaseModel):
    """Schema for updating task priority"""
    priority: str  # TaskPriority name


class ConcurrencyLimitsUpdate(BaseModel):
    """Schema for updating concurrency limits"""
    global_limit: Optional[int] = None
    per_user_limit: Optional[int] = None


class ResourceThresholdsUpdate(BaseModel):
    """Schema for updating resource thresholds"""
    memory_threshold: Optional[int] = None  # Percentage
    cpu_threshold: Optional[int] = None     # Percentage


class ConfigurationResponse(BaseModel):
    """Response schema for configuration"""
    global_concurrent_limit: int
    per_user_limit: int
    memory_threshold: int
    cpu_threshold: int
    max_queue_size: int
    is_queue_running: bool


class ResourceStatusResponse(BaseModel):
    """Response schema for resource status"""
    memory_usage_mb: float
    cpu_usage_percent: float
    resource_available: bool


# ============================================================================
# QUEUE STATUS ENDPOINTS
# ============================================================================

@router.get("/status", response_model=QueueStatusResponse)
async def get_queue_status(current_user: Dict[str, Any] = Depends(require_auth)):
    """
    Get overall queue status
    """
    try:
        status = task_queue.get_queue_status()
        return QueueStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get queue status"
        )


@router.get("/status/user", response_model=UserQueueStatusResponse)
async def get_user_queue_status(current_user: Dict[str, Any] = Depends(require_auth)):
    """
    Get queue status for current user
    """
    try:
        user_status = task_queue.get_user_queue_status(current_user["user_id"])
        return UserQueueStatusResponse(**user_status)
        
    except Exception as e:
        logger.error(f"Error getting user queue status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user queue status"
        )


@router.get("/status/resources", response_model=ResourceStatusResponse)
async def get_resource_status(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Get system resource status (admin only)
    """
    try:
        available = resource_monitor.check_resource_availability()
        memory_usage = resource_monitor._get_memory_usage()
        cpu_usage = resource_monitor._get_cpu_usage()
        
        return ResourceStatusResponse(
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            resource_available=available
        )
        
    except Exception as e:
        logger.error(f"Error getting resource status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get resource status"
        )


# ============================================================================
# QUEUE MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/start")
async def start_queue(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Start the task queue worker (admin only)
    """
    try:
        task_queue.start()
        return {"message": "Task queue started successfully"}
        
    except Exception as e:
        logger.error(f"Error starting queue: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start task queue"
        )


@router.post("/stop")
async def stop_queue(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Stop the task queue worker (admin only)
    """
    try:
        task_queue.stop()
        return {"message": "Task queue stopped successfully"}
        
    except Exception as e:
        logger.error(f"Error stopping queue: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop task queue"
        )


@router.put("/tasks/{task_id}/priority")
async def update_task_priority(
    task_id: str,
    priority_data: TaskPriorityUpdate,
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Update task priority (user can only update their own tasks)
    """
    try:
        # Validate priority
        try:
            new_priority = TaskPriority[priority_data.priority.upper()]
        except KeyError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority: {priority_data.priority}"
            )
        
        # Check task ownership
        from src.services.task_manager import task_manager
        if not task_manager.check_task_ownership(task_id, current_user["user_id"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to modify this task"
            )
        
        # Update priority
        success = task_queue.update_task_priority(task_id, new_priority)
        
        if success:
            return {"message": f"Task priority updated to {new_priority.name}"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update task priority"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating task priority: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update task priority"
        )


# ============================================================================
# STATISTICS ENDPOINTS
# ============================================================================

@router.get("/stats/priorities")
async def get_priority_stats(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Get task priority statistics (admin only)
    """
    try:
        stats = task_queue.get_priority_stats()
        return {"priority_stats": stats}
        
    except Exception as e:
        logger.error(f"Error getting priority stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get priority statistics"
        )


@router.get("/stats/concurrency")
async def get_concurrency_stats(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Get concurrency statistics (admin only)
    """
    try:
        user_counts = concurrency_manager.get_all_counts()
        total_running = sum(user_counts.values())
        
        return {
            "total_running_tasks": total_running,
            "max_global_concurrent": concurrency_manager.global_limit,
            "max_per_user_concurrent": concurrency_manager.per_user_limit,
            "user_task_counts": user_counts,
            "available_global_slots": max(0, concurrency_manager.global_limit - total_running)
        }
        
    except Exception as e:
        logger.error(f"Error getting concurrency stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get concurrency statistics"
        )


@router.get("/stats/performance")
async def get_performance_stats(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Get performance statistics (admin only)
    """
    try:
        # Clean up old metrics first
        resource_monitor.cleanup_old_metrics()
        
        # Get recent task metrics
        recent_metrics = []
        for task_id, metrics in list(resource_monitor.task_metrics.items())[-10:]:  # Last 10 tasks
            if "duration" in metrics:
                recent_metrics.append({
                    "task_id": task_id,
                    "duration": metrics["duration"],
                    "success": metrics.get("success", False),
                    "memory_used": metrics.get("memory_end", 0) - metrics.get("memory_start", 0)
                })
        
        # Calculate averages
        if recent_metrics:
            avg_duration = sum(m["duration"] for m in recent_metrics) / len(recent_metrics)
            success_rate = sum(1 for m in recent_metrics if m["success"]) / len(recent_metrics)
        else:
            avg_duration = 0
            success_rate = 0
        
        return {
            "recent_tasks": recent_metrics,
            "average_duration": avg_duration,
            "success_rate": success_rate,
            "total_metrics_tracked": len(resource_monitor.task_metrics)
        }
        
    except Exception as e:
        logger.error(f"Error getting performance stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get performance statistics"
        )


# ============================================================================
# CONFIGURATION ENDPOINTS
# ============================================================================

@router.get("/config", response_model=ConfigurationResponse)
async def get_configuration(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Get current queue configuration (admin only)
    """
    try:
        return ConfigurationResponse(
            global_concurrent_limit=concurrency_manager.global_limit,
            per_user_limit=concurrency_manager.per_user_limit,
            memory_threshold=resource_monitor.resource_limits["max_memory_mb"],
            cpu_threshold=resource_monitor.resource_limits["max_cpu_percent"],
            max_queue_size=resource_monitor.resource_limits["max_queue_size"],
            is_queue_running=task_queue.is_running
        )

    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get configuration"
        )


@router.put("/config/concurrency")
async def update_concurrency_limits(
    limits: ConcurrencyLimitsUpdate,
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """
    Update concurrency limits (admin only)
    """
    try:
        if limits.global_limit is not None:
            concurrency_manager.global_limit = limits.global_limit
            task_queue.max_concurrent_tasks = limits.global_limit

        if limits.per_user_limit is not None:
            concurrency_manager.per_user_limit = limits.per_user_limit

        return {
            "message": "Concurrency limits updated successfully",
            "global_limit": concurrency_manager.global_limit,
            "per_user_limit": concurrency_manager.per_user_limit
        }

    except Exception as e:
        logger.error(f"Error updating concurrency limits: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update concurrency limits"
        )


@router.put("/config/resources")
async def update_resource_thresholds(
    thresholds: ResourceThresholdsUpdate,
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """
    Update resource monitoring thresholds (admin only)
    """
    try:
        if thresholds.memory_threshold is not None:
            resource_monitor.resource_limits["max_memory_mb"] = thresholds.memory_threshold

        if thresholds.cpu_threshold is not None:
            resource_monitor.resource_limits["max_cpu_percent"] = thresholds.cpu_threshold

        return {
            "message": "Resource thresholds updated successfully",
            "memory_threshold": resource_monitor.resource_limits["max_memory_mb"],
            "cpu_threshold": resource_monitor.resource_limits["max_cpu_percent"]
        }

    except Exception as e:
        logger.error(f"Error updating resource thresholds: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update resource thresholds"
        )


# ============================================================================
# MAINTENANCE ENDPOINTS
# ============================================================================

@router.post("/cleanup/metrics")
async def cleanup_old_metrics(current_user: Dict[str, Any] = Depends(require_admin)):
    """
    Clean up old performance metrics (admin only)
    """
    try:
        resource_monitor.cleanup_old_metrics()
        return {"message": "Old metrics cleaned up successfully"}
        
    except Exception as e:
        logger.error(f"Error cleaning up metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clean up metrics"
        )
