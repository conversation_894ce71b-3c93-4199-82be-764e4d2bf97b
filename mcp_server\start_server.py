#!/usr/bin/env python3
"""
Standalone script to start the SEO Analytics MCP Server
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from mcp_server.server import SEOAnalyticsMCPServer
from mcp.server.stdio import stdio_server
from mcp.server.models import InitializationOptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point"""
    logger.info("Starting SEO Analytics MCP Server...")
    
    # Check required environment variables
    required_env_vars = ["SUPABASE_URL", "SUPABASE_KEY", "JWT_SECRET_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set the following environment variables:")
        for var in missing_vars:
            logger.error(f"  export {var}=your_value_here")
        sys.exit(1)
    
    try:
        # Create server instance
        server_instance = SEOAnalyticsMCPServer()
        
        # Run with stdio transport
        async with stdio_server() as (read_stream, write_stream):
            await server_instance.initialize()
            logger.info("Server initialized successfully")
            
            await server_instance.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="seo-analytics",
                    server_version="1.0.0",
                    capabilities=server_instance.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
