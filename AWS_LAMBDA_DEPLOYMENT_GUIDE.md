# AWS Lambda Deployment Guide

## ⚠️ Current Architecture Challenges

### Issues with Direct Lambda Deployment:
1. **Long-running tasks** (5-30 min) vs Lambda 15-min limit
2. **Persistent background queue** vs Lambda stateless model
3. **In-memory task management** vs Lambda cold starts
4. **Continuous worker processes** vs event-driven execution

## 🔄 Lambda-Compatible Architecture

### Option 1: Microservices with SQS (Recommended)

```
┌─────────────────────────────────────────────────────────────┐
│                    LAMBDA ARCHITECTURE                     │
├─────────────────────────────────────────────────────────────┤
│ API Gateway → Lambda Functions:                             │
│ ├─ auth-service (Authentication)                           │
│ ├─ task-manager (Create/List/Cancel tasks)                 │
│ ├─ queue-manager (Queue status/management)                 │
│ └─ task-processor (Process individual tasks)               │
├─────────────────────────────────────────────────────────────┤
│ AWS SQS → Task Queue:                                       │
│ ├─ task-queue.fifo (Main task queue)                      │
│ ├─ retry-queue.fifo (Failed task retries)                 │
│ └─ priority-queue (High priority tasks)                    │
├─────────────────────────────────────────────────────────────┤
│ AWS Step Functions → Long-running Workflows:               │
│ ├─ SEO Analysis Workflow                                   │
│ ├─ Report Generation Workflow                              │
│ └─ Data Crawling Workflow                                  │
├─────────────────────────────────────────────────────────────┤
│ Storage:                                                    │
│ ├─ RDS/Supabase (User data, task metadata)                │
│ ├─ S3 (Generated reports, temp files)                     │
│ └─ DynamoDB (Session management, cache)                    │
└─────────────────────────────────────────────────────────────┘
```

### Option 2: Hybrid Architecture (Easier Migration)

```
┌─────────────────────────────────────────────────────────────┐
│                   HYBRID ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────┤
│ Lambda Functions (API Layer):                              │
│ ├─ Authentication APIs                                     │
│ ├─ Task Management APIs                                    │
│ └─ Queue Status APIs                                       │
├─────────────────────────────────────────────────────────────┤
│ ECS/Fargate (Worker Layer):                                │
│ ├─ Task Queue Manager (Current implementation)             │
│ ├─ Background Workers                                      │
│ └─ Long-running Processes                                  │
├─────────────────────────────────────────────────────────────┤
│ Shared Storage:                                             │
│ ├─ RDS (Task data)                                         │
│ └─ Redis (Queue state)                                     │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ Migration Process

### Phase 1: Prepare Lambda-Compatible Code

1. **Split into Microservices**
```bash
# Create separate Lambda functions
mkdir lambda-functions
├── auth-service/
├── task-manager/
├── queue-manager/
└── task-processor/
```

2. **Replace In-Memory Queue with SQS**
```python
# Replace TaskQueueManager with SQS
import boto3

class SQSTaskQueue:
    def __init__(self):
        self.sqs = boto3.client('sqs')
        self.queue_url = 'https://sqs.region.amazonaws.com/account/task-queue'
    
    def enqueue_task(self, task_data):
        self.sqs.send_message(
            QueueUrl=self.queue_url,
            MessageBody=json.dumps(task_data),
            MessageAttributes={
                'priority': {'StringValue': str(priority), 'DataType': 'Number'}
            }
        )
```

3. **Convert Long Tasks to Step Functions**
```yaml
# step-function-definition.yaml
Comment: "SEO Analysis Workflow"
StartAt: "StartAnalysis"
States:
  StartAnalysis:
    Type: "Task"
    Resource: "arn:aws:lambda:region:account:function:start-analysis"
    Next: "ProcessData"
  ProcessData:
    Type: "Task"
    Resource: "arn:aws:lambda:region:account:function:process-data"
    Next: "GenerateReport"
  GenerateReport:
    Type: "Task"
    Resource: "arn:aws:lambda:region:account:function:generate-report"
    End: true
```

### Phase 2: Deploy Infrastructure

1. **Terraform/CloudFormation Setup**
```hcl
# main.tf
resource "aws_lambda_function" "auth_service" {
  filename         = "auth-service.zip"
  function_name    = "seo-auth-service"
  role            = aws_iam_role.lambda_role.arn
  handler         = "handler.main"
  runtime         = "python3.9"
  timeout         = 30
}

resource "aws_sqs_queue" "task_queue" {
  name                      = "seo-task-queue.fifo"
  fifo_queue               = true
  content_based_deduplication = true
}

resource "aws_api_gateway_rest_api" "seo_api" {
  name = "seo-analysis-api"
}
```

2. **Environment Variables**
```bash
# Lambda environment variables
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
JWT_SECRET_KEY=your_jwt_secret
SQS_QUEUE_URL=your_sqs_queue_url
S3_BUCKET=your_s3_bucket
```

### Phase 3: Code Modifications

1. **Lambda Handler Pattern**
```python
# auth-service/handler.py
import json
from src.services.auth_service import auth_service

def lambda_handler(event, context):
    try:
        # Parse API Gateway event
        http_method = event['httpMethod']
        path = event['path']
        body = json.loads(event.get('body', '{}'))
        
        if path == '/auth/login' and http_method == 'POST':
            result = auth_service.login_user(
                body['email_or_username'],
                body['password']
            )
            
            return {
                'statusCode': 200,
                'body': json.dumps(result),
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }
```

2. **SQS Task Processor**
```python
# task-processor/handler.py
def lambda_handler(event, context):
    for record in event['Records']:
        task_data = json.loads(record['body'])
        
        # Process task
        try:
            result = process_seo_task(task_data)
            
            # Update task status in database
            update_task_status(task_data['task_id'], 'completed', result)
            
        except Exception as e:
            # Send to retry queue or mark as failed
            handle_task_failure(task_data, str(e))
```

## 📊 Cost Comparison

### Current Server Architecture:
```
512GB Server: $500-2000/month
Always running: 24/7 costs
Fixed capacity: Pay for peak usage
```

### Lambda Architecture:
```
API Calls: $0.20 per 1M requests
Compute: $0.0000166667 per GB-second
SQS: $0.40 per 1M requests
Step Functions: $0.025 per 1K transitions

Estimated monthly cost for 10K tasks: $50-200
Pay-per-use: Only pay for actual usage
Auto-scaling: Handle traffic spikes automatically
```

## 🚀 Deployment Commands

```bash
# 1. Package Lambda functions
cd lambda-functions/auth-service
zip -r auth-service.zip .

# 2. Deploy with Serverless Framework
npm install -g serverless
serverless deploy --stage prod

# 3. Or use AWS SAM
sam build
sam deploy --guided

# 4. Or use Terraform
terraform init
terraform plan
terraform apply
```

## 🔧 Configuration for Lambda

### Recommended Settings:
```yaml
# serverless.yml
service: seo-analysis-tool

provider:
  name: aws
  runtime: python3.9
  region: us-east-1
  timeout: 30
  memorySize: 1024

functions:
  auth:
    handler: auth.handler
    events:
      - http:
          path: /auth/{proxy+}
          method: ANY
          cors: true
  
  tasks:
    handler: tasks.handler
    events:
      - http:
          path: /tasks/{proxy+}
          method: ANY
          cors: true
  
  processor:
    handler: processor.handler
    events:
      - sqs:
          arn: arn:aws:sqs:region:account:task-queue
          batchSize: 1
```

## ⚡ Benefits of Lambda Architecture

1. **Cost Efficiency**: Pay only for actual usage
2. **Auto-Scaling**: Handle any load automatically
3. **No Server Management**: AWS handles infrastructure
4. **High Availability**: Built-in redundancy
5. **Global Distribution**: Deploy to multiple regions

## ⚠️ Considerations

1. **Cold Starts**: 1-5 second delay for new requests
2. **Timeout Limits**: 15 minutes maximum per function
3. **Complexity**: More moving parts to manage
4. **Vendor Lock-in**: AWS-specific architecture
5. **Debugging**: More complex distributed debugging

## 🎯 Recommendation

For your use case with 512GB server available:
1. **Keep current architecture** for maximum performance
2. **Use Lambda for API layer** + **ECS for workers** (hybrid)
3. **Full Lambda migration** only if cost optimization is critical
