#!/usr/bin/env python3
"""
SEO Analytics MCP Server

Provides authenticated access to Supabase-stored SEO analytics data
through Model Context Protocol (MCP) tools.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Sequence
from datetime import datetime, timezone

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Import project modules
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.services.auth_service import AuthService
from src.database.supabase_client import SupabaseClient
from src.config.settings import Settings

# Import MCP tool modules
from .analysis_tools import get_analysis_tools, handle_analysis_tool
from .data_tools import get_data_tools, handle_data_tool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SEOAnalyticsMCPServer:
    """MCP Server for SEO Analytics with user authentication"""
    
    def __init__(self):
        self.server = Server("seo-analytics")
        self.auth_service = None
        self.settings = None
        self._setup_handlers()
    
    async def initialize(self):
        """Initialize server components"""
        try:
            # Load settings
            self.settings = Settings()
            
            # Initialize auth service
            self.auth_service = AuthService(
                supabase_url=self.settings.supabase_url,
                supabase_key=self.settings.supabase_key
            )
            
            logger.info("SEO Analytics MCP Server initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize server: {e}")
            raise
    
    def _setup_handlers(self):
        """Set up MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """List available tools"""
            # Combine all tool definitions
            site_management_tools = [
                # Site Management Tools
                Tool(
                    name="list_user_sites",
                    description="List all sites owned by the authenticated user",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "auth_token": {
                                "type": "string",
                                "description": "JWT authentication token"
                            }
                        },
                        "required": ["auth_token"]
                    }
                ),
                    Tool(
                        name="get_site_details",
                        description="Get detailed information about a specific site",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID to get details for"
                                }
                            },
                            "required": ["auth_token", "site_id"]
                        }
                    ),
                    Tool(
                        name="create_site",
                        description="Create a new site with configuration",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "domain": {
                                    "type": "string",
                                    "description": "Site domain"
                                },
                                "domain_property": {
                                    "type": "string",
                                    "description": "Google Search Console domain property"
                                },
                                "ga_property_id": {
                                    "type": "string",
                                    "description": "Google Analytics property ID"
                                },
                                "homepage": {
                                    "type": "string",
                                    "description": "Homepage URL (optional)"
                                },
                                "wp_api_key": {
                                    "type": "string",
                                    "description": "WordPress API key (optional)"
                                },
                                "service_account_data": {
                                    "type": "object",
                                    "description": "Google service account JSON data (optional)"
                                }
                            },
                            "required": ["auth_token", "domain", "domain_property", "ga_property_id"]
                        }
                    ),
                    Tool(
                        name="update_site_config",
                        description="Update site configuration",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID to update"
                                },
                                "domain_property": {
                                    "type": "string",
                                    "description": "Google Search Console domain property"
                                },
                                "ga_property_id": {
                                    "type": "string",
                                    "description": "Google Analytics property ID"
                                },
                                "homepage": {
                                    "type": "string",
                                    "description": "Homepage URL"
                                },
                                "wp_api_key": {
                                    "type": "string",
                                    "description": "WordPress API key"
                                },
                                "service_account_data": {
                                    "type": "object",
                                    "description": "Google service account JSON data"
                                }
                            },
                            "required": ["auth_token", "site_id"]
                        }
                    ),
                    Tool(
                        name="delete_site",
                        description="Delete a site and all associated data",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID to delete"
                                },
                                "confirm": {
                                    "type": "boolean",
                                    "description": "Confirmation flag (must be true)"
                                }
                            },
                            "required": ["auth_token", "site_id", "confirm"]
                        }
                    ),
                    # SEO Data Retrieval Tools
                    Tool(
                        name="get_site_pages",
                        description="Retrieve pages data for a site with filtering and pagination",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Number of records to return (default: 100, max: 1000)"
                                },
                                "offset": {
                                    "type": "integer",
                                    "description": "Number of records to skip (default: 0)"
                                },
                                "search": {
                                    "type": "string",
                                    "description": "Search term for URL, title, or content"
                                },
                                "date_from": {
                                    "type": "string",
                                    "description": "Filter by snapshot date from (YYYY-MM-DD)"
                                },
                                "date_to": {
                                    "type": "string",
                                    "description": "Filter by snapshot date to (YYYY-MM-DD)"
                                }
                            },
                            "required": ["auth_token", "site_id"]
                        }
                    ),
                    Tool(
                        name="get_site_keywords",
                        description="Retrieve GSC keywords data for a site",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Number of records to return (default: 100, max: 1000)"
                                },
                                "offset": {
                                    "type": "integer",
                                    "description": "Number of records to skip (default: 0)"
                                },
                                "month": {
                                    "type": "string",
                                    "description": "Filter by month (YYYY-MM)"
                                },
                                "keyword": {
                                    "type": "string",
                                    "description": "Search for specific keyword"
                                },
                                "url": {
                                    "type": "string",
                                    "description": "Filter by URL"
                                }
                            },
                            "required": ["auth_token", "site_id"]
                        }
                    ),
                    Tool(
                        name="get_site_traffic",
                        description="Retrieve GSC traffic data for a site",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "auth_token": {
                                    "type": "string",
                                    "description": "JWT authentication token"
                                },
                                "site_id": {
                                    "type": "string",
                                    "description": "Site ID"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Number of records to return (default: 100, max: 1000)"
                                },
                                "offset": {
                                    "type": "integer",
                                    "description": "Number of records to skip (default: 0)"
                                },
                                "month": {
                                    "type": "string",
                                    "description": "Filter by month (YYYY-MM)"
                                },
                                "url": {
                                    "type": "string",
                                    "description": "Filter by URL"
                                }
                            },
                            "required": ["auth_token", "site_id"]
                        }
                    )
                ]

            # Get additional tools from modules
            analysis_tools = get_analysis_tools()
            data_tools = get_data_tools()

            # Combine all tools
            all_tools = site_management_tools + analysis_tools + data_tools

            return ListToolsResult(tools=all_tools)
        
        @self.server.call_tool()
        async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
            """Handle tool calls"""
            try:
                # Extract auth token
                auth_token = request.params.arguments.get("auth_token")
                if not auth_token:
                    return CallToolResult(
                        content=[TextContent(type="text", text="Error: auth_token is required")]
                    )
                
                # Authenticate user
                user = await self.auth_service.get_user_by_token(auth_token)
                if not user:
                    return CallToolResult(
                        content=[TextContent(type="text", text="Error: Invalid or expired authentication token")]
                    )
                
                # Route to appropriate handler
                tool_name = request.params.name

                # Site management tools
                if tool_name in ["list_user_sites", "get_site_details", "create_site", "update_site_config", "delete_site"]:
                    if tool_name == "list_user_sites":
                        return await self._handle_list_user_sites(user, request.params.arguments)
                    elif tool_name == "get_site_details":
                        return await self._handle_get_site_details(user, request.params.arguments)
                    elif tool_name == "create_site":
                        return await self._handle_create_site(user, request.params.arguments)
                    elif tool_name == "update_site_config":
                        return await self._handle_update_site_config(user, request.params.arguments)
                    elif tool_name == "delete_site":
                        return await self._handle_delete_site(user, request.params.arguments)

                # SEO data retrieval tools
                elif tool_name in ["get_site_pages", "get_site_keywords", "get_site_traffic"]:
                    if tool_name == "get_site_pages":
                        return await self._handle_get_site_pages(user, request.params.arguments)
                    elif tool_name == "get_site_keywords":
                        return await self._handle_get_site_keywords(user, request.params.arguments)
                    elif tool_name == "get_site_traffic":
                        return await self._handle_get_site_traffic(user, request.params.arguments)

                # Analysis and reporting tools
                elif tool_name in ["start_site_analysis", "get_task_status", "get_task_logs", "list_user_tasks",
                                 "generate_excel_report", "get_internal_links", "get_analytics_data", "get_monthly_trends"]:
                    return await handle_analysis_tool(tool_name, user, request.params.arguments, self.auth_service, self.settings)

                # Data export and utility tools
                elif tool_name in ["export_site_data", "get_data_summary", "cleanup_old_data", "validate_site_config",
                                 "get_available_dates", "search_pages", "get_site_statistics"]:
                    return await handle_data_tool(tool_name, user, request.params.arguments, self.auth_service, self.settings)

                else:
                    return CallToolResult(
                        content=[TextContent(type="text", text=f"Error: Unknown tool '{tool_name}'")]
                    )
                    
            except Exception as e:
                logger.error(f"Error handling tool call: {e}")
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error: {str(e)}")]
                )
    
    async def _get_user_supabase_client(self, user: Dict[str, Any], domain: str = None) -> SupabaseClient:
        """Get Supabase client for authenticated user"""
        return SupabaseClient(
            url=self.settings.supabase_url,
            key=self.settings.supabase_key,
            domain=domain,
            user_id=user["user_id"]
        )
    
    async def _verify_site_ownership(self, user: Dict[str, Any], site_id: str) -> bool:
        """Verify that user owns the specified site"""
        try:
            client = await self._get_user_supabase_client(user)
            result = client.supabase.table("sites").select("id").eq("id", site_id).eq("user_id", user["user_id"]).execute()
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"Error verifying site ownership: {e}")
            return False

    # Site Management Tool Handlers

    async def _handle_list_user_sites(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle list_user_sites tool call"""
        try:
            client = await self._get_user_supabase_client(user)

            # Get sites owned by user
            result = client.supabase.table("sites").select(
                "id, domain, created_at, last_updated, domain_property, ga_property_id, "
                "homepage, wp_api_key, last_analysis_date, stats_pages, stats_keywords, "
                "stats_internal_links, stats_traffic_records, stats_external_links, "
                "stats_analytics_records, stats_last_updated"
            ).eq("user_id", user["user_id"]).order("created_at", desc=True).execute()

            sites_data = []
            for site in result.data:
                # Calculate data summary
                data_summary = {
                    "pages": site.get("stats_pages", 0) or 0,
                    "keywords": site.get("stats_keywords", 0) or 0,
                    "traffic_records": site.get("stats_traffic_records", 0) or 0,
                    "internal_links": site.get("stats_internal_links", 0) or 0,
                    "external_links": site.get("stats_external_links", 0) or 0,
                    "analytics_records": site.get("stats_analytics_records", 0) or 0,
                    "total_records": (
                        (site.get("stats_pages", 0) or 0) +
                        (site.get("stats_keywords", 0) or 0) +
                        (site.get("stats_traffic_records", 0) or 0) +
                        (site.get("stats_internal_links", 0) or 0) +
                        (site.get("stats_external_links", 0) or 0) +
                        (site.get("stats_analytics_records", 0) or 0)
                    )
                }

                site_info = {
                    "site_id": str(site["id"]),
                    "domain": site["domain"],
                    "created_at": site["created_at"],
                    "last_updated": site.get("last_updated"),
                    "last_analysis_date": site.get("last_analysis_date"),
                    "data_summary": data_summary,
                    "configuration": {
                        "domain_property": site.get("domain_property"),
                        "ga_property_id": site.get("ga_property_id"),
                        "homepage": site.get("homepage"),
                        "has_wp_api_key": bool(site.get("wp_api_key")),
                        "has_service_account": bool(site.get("service_account_data"))
                    }
                }
                sites_data.append(site_info)

            response = {
                "sites": sites_data,
                "total_sites": len(sites_data),
                "user_id": user["user_id"]
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error listing user sites: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_get_site_details(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle get_site_details tool call"""
        try:
            site_id = args.get("site_id")
            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Get site details
            result = client.supabase.table("sites").select("*").eq("id", site_id).execute()
            if not result.data:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found")]
                )

            site = result.data[0]

            # Get available dates and months
            pages_dates = client.supabase.table("pages").select("snapshot_date").eq("site_id", site_id).execute()
            available_dates = list(set([str(row["snapshot_date"]) for row in pages_dates.data]))
            available_dates.sort()

            keywords_months = client.supabase.table("gsc_keywords").select("Month").eq("site_id", site_id).execute()
            available_months = list(set([row["Month"] for row in keywords_months.data]))
            available_months.sort()

            # Prepare response (excluding sensitive data)
            site_details = {
                "site_id": str(site["id"]),
                "domain": site["domain"],
                "created_at": site["created_at"],
                "last_updated": site.get("last_updated"),
                "last_analysis_date": site.get("last_analysis_date"),
                "configuration": {
                    "domain_property": site.get("domain_property"),
                    "ga_property_id": site.get("ga_property_id"),
                    "homepage": site.get("homepage"),
                    "has_wp_api_key": bool(site.get("wp_api_key")),
                    "has_service_account": bool(site.get("service_account_data"))
                },
                "data_summary": {
                    "pages": site.get("stats_pages", 0) or 0,
                    "keywords": site.get("stats_keywords", 0) or 0,
                    "traffic_records": site.get("stats_traffic_records", 0) or 0,
                    "internal_links": site.get("stats_internal_links", 0) or 0,
                    "external_links": site.get("stats_external_links", 0) or 0,
                    "analytics_records": site.get("stats_analytics_records", 0) or 0
                },
                "available_dates": available_dates,
                "available_months": available_months
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(site_details, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error getting site details: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_create_site(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle create_site tool call"""
        try:
            # Extract required parameters
            domain = args.get("domain")
            domain_property = args.get("domain_property")
            ga_property_id = args.get("ga_property_id")

            if not all([domain, domain_property, ga_property_id]):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: domain, domain_property, and ga_property_id are required")]
                )

            client = await self._get_user_supabase_client(user)

            # Check if site already exists for this user
            existing = client.supabase.table("sites").select("id").eq("domain", domain).eq("user_id", user["user_id"]).execute()
            if existing.data:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error: Site with domain '{domain}' already exists")]
                )

            # Prepare site data
            site_data = {
                "domain": domain,
                "domain_property": domain_property,
                "ga_property_id": ga_property_id,
                "user_id": user["user_id"],
                "created_at": datetime.now(timezone.utc).isoformat(),
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

            # Add optional fields
            if args.get("homepage"):
                site_data["homepage"] = args["homepage"]
            if args.get("wp_api_key"):
                site_data["wp_api_key"] = args["wp_api_key"]
            if args.get("service_account_data"):
                site_data["service_account_data"] = args["service_account_data"]

            # Insert site
            result = client.supabase.table("sites").insert(site_data).execute()

            if result.data:
                site = result.data[0]
                response = {
                    "success": True,
                    "message": "Site created successfully",
                    "site": {
                        "site_id": str(site["id"]),
                        "domain": site["domain"],
                        "created_at": site["created_at"]
                    }
                }
                return CallToolResult(
                    content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
                )
            else:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Failed to create site")]
                )

        except Exception as e:
            logger.error(f"Error creating site: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_update_site_config(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle update_site_config tool call"""
        try:
            site_id = args.get("site_id")
            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Prepare update data
            update_data = {
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

            # Add fields to update
            for field in ["domain_property", "ga_property_id", "homepage", "wp_api_key", "service_account_data"]:
                if field in args:
                    update_data[field] = args[field]

            # Update site
            result = client.supabase.table("sites").update(update_data).eq("id", site_id).execute()

            if result.data:
                response = {
                    "success": True,
                    "message": "Site configuration updated successfully",
                    "site_id": site_id,
                    "updated_fields": list(update_data.keys())
                }
                return CallToolResult(
                    content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
                )
            else:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Failed to update site configuration")]
                )

        except Exception as e:
            logger.error(f"Error updating site config: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_delete_site(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle delete_site tool call"""
        try:
            site_id = args.get("site_id")
            confirm = args.get("confirm", False)

            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            if not confirm:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: confirm must be true to delete site")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Get site info before deletion
            site_result = client.supabase.table("sites").select("domain").eq("id", site_id).execute()
            if not site_result.data:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found")]
                )

            domain = site_result.data[0]["domain"]

            # Delete related data first (due to foreign key constraints)
            tables_to_clean = ["pages", "gsc_keywords", "gsc_traffic", "internal_links", "ga_data", "tasks"]

            for table in tables_to_clean:
                try:
                    client.supabase.table(table).delete().eq("site_id", site_id).execute()
                except Exception as e:
                    logger.warning(f"Error deleting from {table}: {e}")

            # Delete site
            result = client.supabase.table("sites").delete().eq("id", site_id).execute()

            response = {
                "success": True,
                "message": f"Site '{domain}' and all associated data deleted successfully",
                "site_id": site_id,
                "domain": domain
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error deleting site: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    # SEO Data Retrieval Tool Handlers

    async def _handle_get_site_pages(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle get_site_pages tool call"""
        try:
            site_id = args.get("site_id")
            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Parse parameters
            limit = min(args.get("limit", 100), 1000)  # Max 1000 records
            offset = args.get("offset", 0)
            search = args.get("search")
            date_from = args.get("date_from")
            date_to = args.get("date_to")

            # Build query
            query = client.supabase.table("pages").select(
                "id, URL, snapshot_date, \"SEO Title\", \"Meta Description\", H1, "
                "\"Page Content\", \"Focus Keyword\", \"Page Type\", Topic, "
                "\"Title Length\", \"GSC Clicks\", \"GSC Impressions\", "
                "\"Google Analytics Page Views\", CTR, Position"
            ).eq("site_id", site_id)

            # Apply filters
            if search:
                # Search in URL, title, or content
                query = query.or_(f"URL.ilike.%{search}%,\"SEO Title\".ilike.%{search}%,\"Page Content\".ilike.%{search}%")

            if date_from:
                query = query.gte("snapshot_date", date_from)

            if date_to:
                query = query.lte("snapshot_date", date_to)

            # Apply pagination and ordering
            query = query.order("snapshot_date", desc=True).range(offset, offset + limit - 1)

            result = query.execute()

            # Get total count for pagination info
            count_query = client.supabase.table("pages").select("id", count="exact").eq("site_id", site_id)
            if search:
                count_query = count_query.or_(f"URL.ilike.%{search}%,\"SEO Title\".ilike.%{search}%,\"Page Content\".ilike.%{search}%")
            if date_from:
                count_query = count_query.gte("snapshot_date", date_from)
            if date_to:
                count_query = count_query.lte("snapshot_date", date_to)

            count_result = count_query.execute()
            total_count = count_result.count

            response = {
                "pages": result.data,
                "pagination": {
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total_count
                },
                "filters": {
                    "search": search,
                    "date_from": date_from,
                    "date_to": date_to
                }
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error getting site pages: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_get_site_keywords(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle get_site_keywords tool call"""
        try:
            site_id = args.get("site_id")
            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Parse parameters
            limit = min(args.get("limit", 100), 1000)
            offset = args.get("offset", 0)
            month = args.get("month")
            keyword = args.get("keyword")
            url = args.get("url")

            # Build query
            query = client.supabase.table("gsc_keywords").select(
                "id, URL, Keyword, Month, Clicks, Impressions, CTR, Position"
            ).eq("site_id", site_id)

            # Apply filters
            if month:
                query = query.eq("Month", month)
            if keyword:
                query = query.ilike("Keyword", f"%{keyword}%")
            if url:
                query = query.ilike("URL", f"%{url}%")

            # Apply pagination and ordering
            query = query.order("Clicks", desc=True).range(offset, offset + limit - 1)

            result = query.execute()

            # Get total count
            count_query = client.supabase.table("gsc_keywords").select("id", count="exact").eq("site_id", site_id)
            if month:
                count_query = count_query.eq("Month", month)
            if keyword:
                count_query = count_query.ilike("Keyword", f"%{keyword}%")
            if url:
                count_query = count_query.ilike("URL", f"%{url}%")

            count_result = count_query.execute()
            total_count = count_result.count

            response = {
                "keywords": result.data,
                "pagination": {
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total_count
                },
                "filters": {
                    "month": month,
                    "keyword": keyword,
                    "url": url
                }
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error getting site keywords: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )

    async def _handle_get_site_traffic(self, user: Dict[str, Any], args: Dict[str, Any]) -> CallToolResult:
        """Handle get_site_traffic tool call"""
        try:
            site_id = args.get("site_id")
            if not site_id:
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: site_id is required")]
                )

            # Verify ownership
            if not await self._verify_site_ownership(user, site_id):
                return CallToolResult(
                    content=[TextContent(type="text", text="Error: Site not found or access denied")]
                )

            client = await self._get_user_supabase_client(user)

            # Parse parameters
            limit = min(args.get("limit", 100), 1000)
            offset = args.get("offset", 0)
            month = args.get("month")
            url = args.get("url")

            # Build query
            query = client.supabase.table("gsc_traffic").select(
                "id, URL, Month, Clicks, Impressions, CTR, Position"
            ).eq("site_id", site_id)

            # Apply filters
            if month:
                query = query.eq("Month", month)
            if url:
                query = query.ilike("URL", f"%{url}%")

            # Apply pagination and ordering
            query = query.order("Clicks", desc=True).range(offset, offset + limit - 1)

            result = query.execute()

            # Get total count
            count_query = client.supabase.table("gsc_traffic").select("id", count="exact").eq("site_id", site_id)
            if month:
                count_query = count_query.eq("Month", month)
            if url:
                count_query = count_query.ilike("URL", f"%{url}%")

            count_result = count_query.execute()
            total_count = count_result.count

            response = {
                "traffic": result.data,
                "pagination": {
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total_count
                },
                "filters": {
                    "month": month,
                    "url": url
                }
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )

        except Exception as e:
            logger.error(f"Error getting site traffic: {e}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Error: {str(e)}")]
            )


async def main():
    """Main entry point for the MCP server"""
    server_instance = SEOAnalyticsMCPServer()

    async with stdio_server() as (read_stream, write_stream):
        await server_instance.initialize()
        await server_instance.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="seo-analytics",
                server_version="1.0.0",
                capabilities=server_instance.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())
