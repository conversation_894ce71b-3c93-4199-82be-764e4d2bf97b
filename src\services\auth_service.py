"""
Authentication service for JWT token management and user authentication
"""
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Tuple
from passlib.context import CryptContext
from email_validator import validate_email, EmailNotValidError

from src.config.settings import settings
from src.database.supabase_client import SUPABASE_AVAILABLE
from src.utils.logging import get_logger

logger = get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
JWT_SECRET_KEY = settings.jwt_secret_key or "your-secret-key-change-in-production"
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7


class AuthenticationError(Exception):
    """Base authentication error"""
    pass


class InvalidCredentialsError(AuthenticationError):
    """Invalid username/password"""
    pass


class UserNotFoundError(AuthenticationError):
    """User not found"""
    pass


class UserAlreadyExistsError(AuthenticationError):
    """User already exists"""
    pass


class TokenExpiredError(AuthenticationError):
    """Token has expired"""
    pass


class InvalidTokenError(AuthenticationError):
    """Invalid token"""
    pass


class AuthService:
    """Authentication service for user management and JWT tokens"""
    
    def __init__(self):
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase client not available")
        
        from supabase import create_client
        self.supabase = create_client(settings.supabase_url, settings.supabase_key)
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def validate_email_format(self, email: str) -> bool:
        """Validate email format"""
        try:
            validate_email(email)
            return True
        except EmailNotValidError:
            return False
    
    def validate_password_strength(self, password: str) -> Tuple[bool, str]:
        """Validate password strength"""
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        if not any(c.isupper() for c in password):
            return False, "Password must contain at least one uppercase letter"
        
        if not any(c.islower() for c in password):
            return False, "Password must contain at least one lowercase letter"
        
        if not any(c.isdigit() for c in password):
            return False, "Password must contain at least one digit"
        
        return True, "Password is strong"
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            if payload.get("type") != token_type:
                raise InvalidTokenError(f"Invalid token type. Expected {token_type}")
            
            return payload
        
        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("Token has expired")
        except jwt.JWTError:
            raise InvalidTokenError("Invalid token")
    
    def hash_token(self, token: str) -> str:
        """Hash token for secure storage"""
        return hashlib.sha256(token.encode()).hexdigest()
    
    async def register_user(self, email: str, username: str, password: str, 
                           first_name: Optional[str] = None, last_name: Optional[str] = None) -> Dict[str, Any]:
        """Register a new user"""
        try:
            # Validate email format
            if not self.validate_email_format(email):
                raise ValueError("Invalid email format")
            
            # Validate password strength
            is_strong, message = self.validate_password_strength(password)
            if not is_strong:
                raise ValueError(message)
            
            # Check if user already exists
            existing_user = self.supabase.table("users").select("id").or_(
                f"email.eq.{email},username.eq.{username}"
            ).execute()
            
            if existing_user.data:
                raise UserAlreadyExistsError("User with this email or username already exists")
            
            # Hash password
            password_hash = self.hash_password(password)
            
            # Create verification token
            verification_token = secrets.token_urlsafe(32)
            verification_expires = datetime.now(timezone.utc) + timedelta(hours=24)
            
            # Insert user
            user_data = {
                "email": email.lower(),
                "username": username,
                "password_hash": password_hash,
                "first_name": first_name,
                "last_name": last_name,
                "verification_token": verification_token,
                "verification_expires": verification_expires.isoformat()
            }
            
            result = self.supabase.table("users").insert(user_data).execute()
            
            if result.data:
                user = result.data[0]
                logger.info(f"User registered successfully: {email}")
                
                return {
                    "user_id": user["id"],
                    "email": user["email"],
                    "username": user["username"],
                    "verification_token": verification_token,
                    "message": "User registered successfully. Please verify your email."
                }
            else:
                raise Exception("Failed to create user")
                
        except Exception as e:
            logger.error(f"User registration failed: {e}")
            raise
    
    async def authenticate_user(self, email_or_username: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return user data"""
        try:
            # Find user by email or username
            user_result = self.supabase.table("users").select("*").or_(
                f"email.eq.{email_or_username.lower()},username.eq.{email_or_username}"
            ).eq("is_active", True).execute()
            
            if not user_result.data:
                raise UserNotFoundError("User not found or inactive")
            
            user = user_result.data[0]
            
            # Verify password
            if not self.verify_password(password, user["password_hash"]):
                raise InvalidCredentialsError("Invalid password")
            
            # Update last login
            self.supabase.table("users").update({
                "last_login": datetime.now(timezone.utc).isoformat()
            }).eq("id", user["id"]).execute()
            
            logger.info(f"User authenticated successfully: {user['email']}")
            
            return {
                "user_id": user["id"],
                "email": user["email"],
                "username": user["username"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "role": user["role"],
                "is_verified": user["is_verified"]
            }
            
        except (UserNotFoundError, InvalidCredentialsError):
            raise
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise AuthenticationError("Authentication failed")
    
    async def login_user(self, email_or_username: str, password: str, 
                        ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict[str, Any]:
        """Login user and create session tokens"""
        try:
            # Authenticate user
            user_data = await self.authenticate_user(email_or_username, password)
            
            # Create tokens
            token_data = {"sub": user_data["user_id"], "email": user_data["email"], "role": user_data["role"]}
            access_token = self.create_access_token(token_data)
            refresh_token = self.create_refresh_token(token_data)
            
            # Store session in database
            session_data = {
                "user_id": user_data["user_id"],
                "token_hash": self.hash_token(access_token),
                "refresh_token_hash": self.hash_token(refresh_token),
                "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)).isoformat(),
                "refresh_expires_at": (datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)).isoformat(),
                "ip_address": ip_address,
                "user_agent": user_agent
            }
            
            self.supabase.table("user_sessions").insert(session_data).execute()
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "user": user_data
            }
            
        except Exception as e:
            logger.error(f"Login failed: {e}")
            raise
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        try:
            # Verify refresh token
            payload = self.verify_token(refresh_token, "refresh")
            user_id = payload["sub"]
            
            # Check if refresh token exists in database
            token_hash = self.hash_token(refresh_token)
            session_result = self.supabase.table("user_sessions").select("*").eq(
                "refresh_token_hash", token_hash
            ).eq("is_active", True).execute()
            
            if not session_result.data:
                raise InvalidTokenError("Invalid refresh token")
            
            session = session_result.data[0]
            
            # Check if refresh token is expired
            if datetime.fromisoformat(session["refresh_expires_at"]) < datetime.now(timezone.utc):
                raise TokenExpiredError("Refresh token has expired")
            
            # Get user data
            user_result = self.supabase.table("users").select("*").eq("id", user_id).execute()
            if not user_result.data:
                raise UserNotFoundError("User not found")
            
            user = user_result.data[0]
            
            # Create new access token
            token_data = {"sub": user["id"], "email": user["email"], "role": user["role"]}
            new_access_token = self.create_access_token(token_data)
            
            # Update session with new access token
            self.supabase.table("user_sessions").update({
                "token_hash": self.hash_token(new_access_token),
                "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)).isoformat(),
                "last_used": datetime.now(timezone.utc).isoformat()
            }).eq("id", session["id"]).execute()
            
            return {
                "access_token": new_access_token,
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise
    
    async def logout_user(self, access_token: str) -> bool:
        """Logout user by invalidating session"""
        try:
            token_hash = self.hash_token(access_token)
            
            # Deactivate session
            result = self.supabase.table("user_sessions").update({
                "is_active": False
            }).eq("token_hash", token_hash).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Logout failed: {e}")
            return False
    
    async def get_user_by_token(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get user data from access token"""
        try:
            # Verify token
            payload = self.verify_token(access_token, "access")
            user_id = payload["sub"]
            
            # Check if token exists in active session
            token_hash = self.hash_token(access_token)
            session_result = self.supabase.table("user_sessions").select("*").eq(
                "token_hash", token_hash
            ).eq("is_active", True).execute()
            
            if not session_result.data:
                return None
            
            # Get user data
            user_result = self.supabase.table("users").select("*").eq("id", user_id).eq("is_active", True).execute()
            if not user_result.data:
                return None
            
            user = user_result.data[0]
            
            return {
                "user_id": user["id"],
                "email": user["email"],
                "username": user["username"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "role": user["role"],
                "is_verified": user["is_verified"]
            }
            
        except Exception as e:
            logger.error(f"Get user by token failed: {e}")
            return None


# Global auth service instance
auth_service = AuthService()
