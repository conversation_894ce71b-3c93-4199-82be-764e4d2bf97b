# SEO Analytics MCP Server Testing Guide

## Test Setup

### 1. Prerequisites

- Running SEO Analytics API server (for authentication)
- Configured Supabase database with test data
- MCP client (<PERSON>, or custom client)
- Valid user account in the system

### 2. Environment Setup

```bash
# Set environment variables
export SUPABASE_URL="your-supabase-url"
export SUPABASE_KEY="your-supabase-key"
export JWT_SECRET_KEY="your-jwt-secret"

# Start the main API server (for authentication)
cd /path/to/your/project
python -m uvicorn src.api.app:app --reload --port 8000

# Start the MCP server (in another terminal)
python mcp_server/start_server.py
```

## Authentication Testing

### 1. Get Authentication Token

```bash
# Login to get JWT token
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email_or_username": "<EMAIL>",
    "password": "your-password"
  }'
```

Expected response:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 2. Test Invalid Token

Try using an invalid token with any tool to verify authentication is working.

## Tool Testing

### Site Management Tools

#### 1. List User Sites

```json
{
  "tool": "list_user_sites",
  "arguments": {
    "auth_token": "your-jwt-token"
  }
}
```

Expected: List of sites owned by the authenticated user.

#### 2. Get Site Details

```json
{
  "tool": "get_site_details",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1"
  }
}
```

Expected: Detailed site information including configuration and data summary.

#### 3. Create Site

```json
{
  "tool": "create_site",
  "arguments": {
    "auth_token": "your-jwt-token",
    "domain": "test-site.com",
    "domain_property": "https://test-site.com/",
    "ga_property_id": "123456789",
    "homepage": "https://test-site.com"
  }
}
```

Expected: Success response with new site ID.

#### 4. Update Site Config

```json
{
  "tool": "update_site_config",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "homepage": "https://updated-homepage.com"
  }
}
```

Expected: Success response confirming update.

#### 5. Delete Site

```json
{
  "tool": "delete_site",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "confirm": true
  }
}
```

Expected: Success response confirming deletion.

### SEO Data Retrieval Tools

#### 1. Get Site Pages

```json
{
  "tool": "get_site_pages",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "limit": 10,
    "search": "keyword"
  }
}
```

Expected: Paginated list of pages with search filtering.

#### 2. Get Site Keywords

```json
{
  "tool": "get_site_keywords",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "limit": 20,
    "month": "2024-01"
  }
}
```

Expected: List of GSC keywords for the specified month.

#### 3. Get Site Traffic

```json
{
  "tool": "get_site_traffic",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "limit": 15
  }
}
```

Expected: List of GSC traffic data.

### Analysis & Reporting Tools

#### 1. Start Site Analysis

```json
{
  "tool": "start_site_analysis",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "analysis_type": "incremental"
  }
}
```

Expected: Task ID for monitoring progress.

#### 2. Get Task Status

```json
{
  "tool": "get_task_status",
  "arguments": {
    "auth_token": "your-jwt-token",
    "task_id": "task-uuid-here"
  }
}
```

Expected: Current task status and progress.

#### 3. List User Tasks

```json
{
  "tool": "list_user_tasks",
  "arguments": {
    "auth_token": "your-jwt-token",
    "status": "completed",
    "limit": 10
  }
}
```

Expected: List of user's tasks with filtering.

### Data Export & Utility Tools

#### 1. Export Site Data

```json
{
  "tool": "export_site_data",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "data_types": ["pages", "keywords"],
    "format": "json"
  }
}
```

Expected: Exported data in specified format.

#### 2. Get Data Summary

```json
{
  "tool": "get_data_summary",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1"
  }
}
```

Expected: Comprehensive data summary with counts and date ranges.

#### 3. Get Available Dates

```json
{
  "tool": "get_available_dates",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1"
  }
}
```

Expected: List of available analysis dates and months.

## Security Testing

### 1. Cross-User Access Test

- Create two user accounts
- Login as User A, get sites
- Try to access User B's sites with User A's token
- Expected: Access denied errors

### 2. Token Expiration Test

- Use an expired token
- Expected: Authentication failure

### 3. Invalid Site ID Test

- Use a non-existent site ID
- Expected: Site not found error

## Error Handling Testing

### 1. Missing Parameters

Test each tool with missing required parameters.

### 2. Invalid Parameters

Test with invalid data types, out-of-range values, etc.

### 3. Database Connection Issues

Temporarily disable database connection to test error handling.

## Performance Testing

### 1. Large Dataset Queries

Test pagination with large datasets:

```json
{
  "tool": "get_site_pages",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "1",
    "limit": 1000,
    "offset": 0
  }
}
```

### 2. Concurrent Requests

Test multiple simultaneous tool calls.

### 3. Memory Usage

Monitor memory usage during large data exports.

## Integration Testing

### 1. End-to-End Workflow

1. Create site
2. Start analysis
3. Monitor task progress
4. Retrieve data
5. Generate report
6. Export data
7. Clean up

### 2. MCP Client Integration

Test with different MCP clients:
- Claude Desktop
- Custom MCP client
- Command-line MCP tools

## Automated Testing

### Test Script Example

```python
import asyncio
import json
from mcp_client import MCPClient

async def test_mcp_server():
    client = MCPClient("seo-analytics")
    
    # Test authentication
    token = "your-jwt-token"
    
    # Test list sites
    result = await client.call_tool("list_user_sites", {"auth_token": token})
    assert result["sites"] is not None
    
    # Test get site details
    if result["sites"]:
        site_id = result["sites"][0]["site_id"]
        details = await client.call_tool("get_site_details", {
            "auth_token": token,
            "site_id": site_id
        })
        assert details["domain"] is not None
    
    print("All tests passed!")

if __name__ == "__main__":
    asyncio.run(test_mcp_server())
```

## Test Checklist

- [ ] Authentication works correctly
- [ ] All site management tools function
- [ ] Data retrieval tools return correct data
- [ ] Pagination works properly
- [ ] Filtering and search work
- [ ] Analysis tools create tasks correctly
- [ ] Export tools generate correct output
- [ ] Error handling works for all edge cases
- [ ] Security controls prevent cross-user access
- [ ] Performance is acceptable for large datasets
- [ ] Integration with MCP clients works
- [ ] Documentation is accurate and complete
