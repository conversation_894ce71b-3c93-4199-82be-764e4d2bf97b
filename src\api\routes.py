"""
API routes for the SEO analysis application
"""
import os
import uuid
import asyncio
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, BackgroundTasks, Body, Response, Depends
from fastapi.responses import FileResponse

from src.models.schemas import (
    ConfigSchema, ExcelRequestSchema, EnhancedExcelRequestSchema,
    TaskResponse, TaskStatus, TaskDetail, TaskListResponse, TaskLogEntry, TaskLogsResponse,
    SitesListResponse, ReAnalysisRequestSchema,
    UpdateSiteConfigSchema, DeleteSiteDataSchema, AddSiteSchema, AddSiteAndAnalyzeSchema
)
from src.services.analysis_service import SEOAnalysisService
from src.services.task_manager import task_manager, TaskType, TaskLogger
from src.middleware.auth_middleware import require_auth, optional_auth, RequireSiteOwnership, RequireTaskOwnership
from src.database.supabase_client import <PERSON><PERSON><PERSON><PERSON><PERSON>, SUPABASE_AVAILABLE
from src.utils.file_utils import create_temp_json_file, cleanup_temp_file
from src.utils.logging import get_logger
from src.config.settings import settings

logger = get_logger(__name__)

# Thread-safe task progress storage using threading.Lock
import threading
from collections import defaultdict

class ThreadSafeTaskProgress:
    """Thread-safe task progress storage"""
    def __init__(self):
        self._progress = {}
        self._lock = threading.Lock()

    def get(self, task_id: str) -> Dict[str, Any]:
        with self._lock:
            return self._progress.get(task_id, {})

    def set(self, task_id: str, data: Dict[str, Any]):
        with self._lock:
            self._progress[task_id] = data

    def update(self, task_id: str, updates: Dict[str, Any]):
        with self._lock:
            if task_id not in self._progress:
                self._progress[task_id] = {}
            self._progress[task_id].update(updates)

    def exists(self, task_id: str) -> bool:
        with self._lock:
            return task_id in self._progress

    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Remove tasks older than max_age_hours"""
        from datetime import datetime, timedelta
        cutoff = datetime.now() - timedelta(hours=max_age_hours)

        with self._lock:
            to_remove = []
            for task_id, data in self._progress.items():
                if 'created_at' in data:
                    created_at = datetime.fromisoformat(data['created_at'])
                    if created_at < cutoff:
                        to_remove.append(task_id)

            for task_id in to_remove:
                del self._progress[task_id]

# Global thread-safe task progress storage
task_progress = ThreadSafeTaskProgress()



# Initialize services
analysis_service = SEOAnalysisService()

# Create router
router = APIRouter()


def update_task_progress(task_id: str, progress: int, message: str,
                        result: Dict[str, Any] = None, error: str = None):
    """Update task progress using database-backed task manager"""
    try:
        task_manager.update_task_progress(task_id, progress, message, result, error)
    except Exception as e:
        logger.error(f"Failed to update task progress for {task_id}: {e}")
        # Fallback to old system for backward compatibility during transition
        task_progress.set(task_id, {
            "status": "failed" if progress == -1 else ("completed" if progress == 100 else "running"),
            "progress": 0 if progress == -1 else progress,
            "message": message,
            "result": result,
            "error": error,
            "updated_at": datetime.now().isoformat()
        })


async def run_seo_analysis(config: Dict[str, Any], task_id: str):
    """Background task for running SEO analysis"""
    try:
        logger.info(f"🚀 BACKGROUND TASK START: Task {task_id} starting analysis")
        await analysis_service.run_analysis(
            config, task_id,
            progress_callback=update_task_progress
        )
        logger.info(f"✅ BACKGROUND TASK COMPLETE: Task {task_id} analysis finished")
    except Exception as e:
        logger.error(f"❌ BACKGROUND TASK FAILED: Task {task_id} error: {str(e)}")
        logger.exception(f"Error in background analysis task {task_id}")
        update_task_progress(task_id, -1, f"Analysis failed: {str(e)}", error=str(e))


async def run_seo_analysis_with_config_save(config: Dict[str, Any], task_id: str):
    """Background task for running SEO analysis and saving configuration"""
    try:
        # Run the analysis first
        await analysis_service.run_analysis(
            config, task_id,
            progress_callback=update_task_progress
        )

        # If analysis succeeded, save the configuration
        if task_progress.get(task_id, {}).get("progress") == 100:
            try:
                update_task_progress(task_id, 100, "Saving site configuration...")

                # Extract domain from domain_property
                from urllib.parse import urlparse
                domain = urlparse(config['domain_property']).netloc

                # Save configuration to Supabase
                if config.get('supabase_url') and config.get('supabase_key'):
                    supabase_client = SupabaseClient(
                        url=config['supabase_url'],
                        key=config['supabase_key'],
                        domain=domain
                    )

                    service_account_data = config.get('_service_account_data')
                    if service_account_data:
                        success, message = supabase_client.save_site_configuration(
                            domain_property=config['domain_property'],
                            ga_property_id=config['ga_property_id'],
                            service_account_data=service_account_data,
                            homepage=config.get('homepage')
                        )
                        if success:
                            logger.info(f"Saved configuration for site {domain}")
                        else:
                            logger.error(f"Failed to save configuration for site {domain}: {message}")

                update_task_progress(task_id, 100, "Analysis completed and configuration saved!")

            except Exception as config_error:
                logger.error(f"Error saving configuration: {config_error}")
                # Don't fail the whole task for configuration save errors
                update_task_progress(task_id, 100, "Analysis completed (configuration save failed)")

    except Exception as e:
        logger.exception(f"Error in background analysis task {task_id}")
        update_task_progress(task_id, -1, f"Analysis failed: {str(e)}", error=str(e))


async def generate_excel_from_supabase(request_data: Dict[str, Any], task_id: str):
    """Background task for generating Excel from Supabase data"""
    try:
        update_task_progress(task_id, 0, "Starting Excel generation from Supabase...")

        domain = request_data['domain']
        date_filter = request_data.get('date')

        # Use environment variables as fallback for Supabase credentials
        supabase_url = request_data.get('supabase_url') or settings.supabase_url
        supabase_key = request_data.get('supabase_key') or settings.supabase_key

        if not supabase_url or not supabase_key:
            raise ValueError("Supabase credentials not provided in request or environment variables")

        # Extract options
        include_raw_data = request_data.get('include_raw_data', True)
        include_keywords = request_data.get('include_keywords', True)
        include_traffic = request_data.get('include_traffic', True)
        include_links = request_data.get('include_links', True)
        include_analytics = request_data.get('include_analytics', True)

        update_task_progress(task_id, 20, f"Connecting to Supabase for domain: {domain}")

        # Initialize Supabase client
        supabase_client = SupabaseClient(
            url=supabase_url,
            key=supabase_key,
            domain=domain
        )

        update_task_progress(task_id, 50, "Generating Excel report from Supabase data...")

        # Generate Excel report from Supabase data with unique session directory
        from src.utils.file_utils import get_output_directory
        output_dir = get_output_directory(f"https://{domain}", session_id=task_id)

        excel_path = supabase_client.generate_excel_report(
            output_dir=output_dir,
            date_filter=date_filter,
            include_raw_data=include_raw_data,
            include_keywords=include_keywords,
            include_traffic=include_traffic,
            include_links=include_links,
            include_analytics=include_analytics
        )

        update_task_progress(task_id, 100, "Excel report generated successfully from Supabase data!",
                           result={"excel_report": excel_path, "domain": domain})

    except Exception as e:
        logger.exception(f"Error generating Excel from Supabase for task {task_id}")
        update_task_progress(task_id, -1, f"Excel generation failed: {str(e)}", error=str(e))


async def generate_excel_from_supabase_enhanced(request_data: Dict[str, Any], task_id: str):
    """Enhanced background task for generating Excel from Supabase data with date range filtering"""
    try:
        update_task_progress(task_id, 0, "Starting enhanced Excel generation from Supabase...")

        domain = request_data['domain']
        site_id = request_data.get('site_id')
        start_date = request_data.get('start_date')
        end_date = request_data.get('end_date')
        date_filter = request_data.get('date')  # Legacy single date filter

        # Use environment variables for Supabase credentials
        supabase_url = settings.supabase_url
        supabase_key = settings.supabase_key

        if not supabase_url or not supabase_key:
            raise ValueError("Supabase credentials not configured in environment variables")

        # Extract options
        include_raw_data = request_data.get('include_raw_data', True)
        include_keywords = request_data.get('include_keywords', True)
        include_traffic = request_data.get('include_traffic', True)
        include_links = request_data.get('include_links', True)
        include_analytics = request_data.get('include_analytics', True)

        update_task_progress(task_id, 20, f"Connecting to Supabase for domain: {domain}")

        # Initialize Supabase client
        supabase_client = SupabaseClient(
            url=supabase_url,
            key=supabase_key,
            domain=domain
        )

        update_task_progress(task_id, 50, "Generating enhanced Excel report with date filtering...")

        # Generate Excel report from Supabase data with enhanced filtering
        from src.utils.file_utils import get_output_directory
        output_dir = get_output_directory(f"https://{domain}")

        # Create a custom Excel generation method that supports date ranges
        excel_path = await generate_enhanced_excel_report(
            supabase_client=supabase_client,
            output_dir=output_dir,
            start_date=start_date,
            end_date=end_date,
            date_filter=date_filter,
            include_raw_data=include_raw_data,
            include_keywords=include_keywords,
            include_traffic=include_traffic,
            include_links=include_links,
            include_analytics=include_analytics
        )

        update_task_progress(task_id, 100, "Enhanced Excel report generated successfully!",
                           result={"excel_report": excel_path, "domain": domain, "site_id": site_id})

    except Exception as e:
        logger.exception(f"Error generating enhanced Excel from Supabase for task {task_id}")
        update_task_progress(task_id, -1, f"Enhanced Excel generation failed: {str(e)}", error=str(e))


async def generate_enhanced_excel_report(supabase_client, output_dir: str,
                                       start_date: str = None, end_date: str = None,
                                       date_filter: str = None, **options) -> str:
    """Generate enhanced Excel report with date range filtering - matches original main.py format"""
    import pandas as pd
    from datetime import datetime
    import os

    logger.info(f"Generating enhanced Excel report for domain: {supabase_client.domain}")

    # Create Excel file path (match original format)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    date_suffix = ""
    if start_date and end_date:
        date_suffix = f"_{start_date}_to_{end_date}"
    elif date_filter:
        date_suffix = f"_{date_filter}"
    elif start_date:
        date_suffix = f"_from_{start_date}"

    excel_path = os.path.join(output_dir, f'report_{supabase_client.domain.replace(".", "_")}{date_suffix}_{timestamp}.xlsx')

    # Use xlsxwriter engine to match original format
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:

        # Get all data first
        pages_df = pd.DataFrame()
        keywords_df = pd.DataFrame()
        ga_df = pd.DataFrame()
        links_df = pd.DataFrame()

        if options.get('include_raw_data', True):
            if start_date or end_date or date_filter:
                pages_df = await get_filtered_pages_data(supabase_client, start_date, end_date, date_filter)
            else:
                pages_df = supabase_client.get_pages_data_for_excel()

        if options.get('include_keywords', True):
            if start_date or end_date:
                keywords_df = await get_filtered_keywords_data(supabase_client, start_date, end_date)
            else:
                keywords_df = supabase_client.get_gsc_keywords_for_excel(date_filter)

        if options.get('include_analytics', True):
            if start_date or end_date:
                ga_df = await get_filtered_ga_data(supabase_client, start_date, end_date)
            else:
                ga_df = supabase_client.get_ga_data_for_excel(date_filter)

        if options.get('include_links', True):
            links_df = await get_filtered_links_data(supabase_client, start_date, end_date, date_filter)

        # Get external links data for SEO analysis
        external_links_df = pd.DataFrame()
        if options.get('include_external_links', True):
            external_links_df = await get_filtered_external_links_data(supabase_client, start_date, end_date, date_filter)

        # 1. Data sheet - comprehensive merged data (main sheet)
        if options.get('include_raw_data', True):
            data_df = supabase_client.create_comprehensive_data_sheet(pages_df, keywords_df, ga_df)
            data_df.to_excel(writer, sheet_name='Data', index=False)

        # 2. Keywords sheet - raw GSC keyword data
        if options.get('include_keywords', True):
            if not keywords_df.empty:
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
            else:
                # Create empty sheet with headers
                empty_df = pd.DataFrame(columns=['page', 'query', 'clicks', 'impressions', 'ctr', 'position', 'Month'])
                empty_df.to_excel(writer, sheet_name='Keywords', index=False)

        # 3. Historical-Traffic sheet - aggregated traffic by URL and month
        if options.get('include_traffic', True):
            hist_traffic_df = supabase_client.create_historical_traffic_sheet(keywords_df)
            hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)

        # 4. Internal-Links sheet (TARGET FORMAT - only 5 columns)
        if options.get('include_links', True):
            if not links_df.empty:
                # Match TARGET format exactly - only 5 columns
                target_links_columns = ['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic']
                links_clean = links_df[[col for col in target_links_columns if col in links_df.columns]]
                links_clean.to_excel(writer, sheet_name='Internal-Links', index=False)
            else:
                # Create empty sheet with TARGET headers (only 5 columns)
                empty_df = pd.DataFrame(columns=['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic'])
                empty_df.to_excel(writer, sheet_name='Internal-Links', index=False)

        # 5. External-Links sheet (SEO analytics for outbound links)
        if options.get('include_external_links', True):
            if not external_links_df.empty:
                # External links with domain analysis for SEO insights
                external_links_df.to_excel(writer, sheet_name='External-Links', index=False)
            else:
                # Create empty sheet with headers for external links analysis
                empty_df = pd.DataFrame(columns=[
                    'URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic', 'Target Domain', 'Authority Level'
                ])
                empty_df.to_excel(writer, sheet_name='External-Links', index=False)

        # NOTE: GA data is merged into the Data sheet, no separate GA-Data sheet in target format

    logger.info(f"Enhanced Excel report generated: {excel_path}")
    return excel_path


async def get_filtered_pages_data(supabase_client, start_date: str = None, end_date: str = None, date_filter: str = None):
    """Get pages data with date filtering (excludes internal database fields)"""
    try:
        # Try to get clean data first, fallback to filtering if needed
        try:
            # Use the clean data method which has fallback logic
            if date_filter:
                return supabase_client.get_pages_data_for_excel(date_filter)
            else:
                return supabase_client.get_pages_data_for_excel()
        except Exception as clean_error:
            logger.warning(f"Clean data method failed, using fallback: {clean_error}")

            # Fallback: get all data and filter manually
            query = supabase_client.client.table('pages').select('*').eq('site_id', supabase_client.site_id)

        if date_filter:
            query = query.eq('snapshot_date', date_filter)
        elif start_date and end_date:
            query = query.gte('snapshot_date', start_date).lte('snapshot_date', end_date)
        elif start_date:
            query = query.gte('snapshot_date', start_date)
        elif end_date:
            query = query.lte('snapshot_date', end_date)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)
                # Remove internal database columns
                columns_to_remove = ['id', 'site_id', 'url_hash', 'snapshot_date', 'raw_html']
                clean_df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])
                return clean_df
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered pages data: {e}")
        return pd.DataFrame()


async def get_filtered_keywords_data(supabase_client, start_date: str = None, end_date: str = None):
    """Get keywords data with month filtering (excludes internal database fields)"""
    try:
        # Try to use clean data method first
        try:
            return supabase_client.get_gsc_keywords_for_excel()
        except Exception as clean_error:
            logger.warning(f"Clean keywords method failed, using fallback: {clean_error}")

            # Fallback: get all data and filter manually
            query = supabase_client.client.table('gsc_keywords').select('*').eq('site_id', supabase_client.site_id)

        if start_date and end_date:
            # Convert dates to month format (YYYY-MM)
            start_month = start_date[:7]  # YYYY-MM-DD -> YYYY-MM
            end_month = end_date[:7]
            query = query.gte('Month', start_month).lte('Month', end_month)
        elif start_date:
            start_month = start_date[:7]
            query = query.gte('Month', start_month)
        elif end_date:
            end_month = end_date[:7]
            query = query.lte('Month', end_month)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)
                # Remove internal database columns
                columns_to_remove = ['id', 'site_id', 'keyword_hash']
                clean_df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])
                return clean_df
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered keywords data: {e}")
        return pd.DataFrame()


async def get_filtered_traffic_data(supabase_client, start_date: str = None, end_date: str = None):
    """Get traffic data with month filtering"""
    try:
        query = supabase_client.client.table('gsc_traffic').select('*').eq('site_id', supabase_client.site_id)

        if start_date and end_date:
            start_month = start_date[:7]
            end_month = end_date[:7]
            query = query.gte('Month', start_month).lte('Month', end_month)
        elif start_date:
            start_month = start_date[:7]
            query = query.gte('Month', start_month)
        elif end_date:
            end_month = end_date[:7]
            query = query.lte('Month', end_month)

        response = query.execute()
        return pd.DataFrame(response.data) if response.data else pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered traffic data: {e}")
        return pd.DataFrame()


async def get_filtered_links_data(supabase_client, start_date: str = None, end_date: str = None, date_filter: str = None):
    """Get internal links data with date filtering (excludes internal database fields)"""
    try:
        # Try to use clean data method first
        try:
            if date_filter:
                return supabase_client.get_internal_links_for_excel(date_filter)
            else:
                return supabase_client.get_internal_links_for_excel()
        except Exception as clean_error:
            logger.warning(f"Clean links method failed, using fallback: {clean_error}")

            # Fallback: get all data and filter manually
            query = supabase_client.client.table('internal_links').select('*').eq('site_id', supabase_client.site_id)

            if date_filter:
                query = query.eq('snapshot_date', date_filter)
            elif start_date and end_date:
                query = query.gte('snapshot_date', start_date).lte('snapshot_date', end_date)
            elif start_date:
                query = query.gte('snapshot_date', start_date)
            elif end_date:
                query = query.lte('snapshot_date', end_date)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)
                # Remove internal database columns
                columns_to_remove = ['id', 'site_id', 'link_hash', 'snapshot_date']
                clean_df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])

                # Filter out external links (keep only Internal and Jump Link types)
                if 'Link Type' in clean_df.columns and not clean_df.empty:
                    internal_types = ['Internal', 'Jump Link', 'internal', 'jump link']
                    clean_df = clean_df[clean_df['Link Type'].isin(internal_types)]

                # Keep only TARGET columns (5 columns)
                target_columns = ['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic']
                clean_df = clean_df[[col for col in target_columns if col in clean_df.columns]]

                return clean_df
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered links data: {e}")
        return pd.DataFrame()


async def get_filtered_ga_data(supabase_client, start_date: str = None, end_date: str = None):
    """Get GA data with month filtering (excludes internal database fields)"""
    try:
        # Try to use clean data method first
        try:
            return supabase_client.get_ga_data_for_excel()
        except Exception as clean_error:
            logger.warning(f"Clean GA method failed, using fallback: {clean_error}")

            # Fallback: get all data and filter manually
            query = supabase_client.client.table('ga_data').select('*').eq('site_id', supabase_client.site_id)

            if start_date and end_date:
                start_month = start_date[:7]
                end_month = end_date[:7]
                query = query.gte('Month', start_month).lte('Month', end_month)
            elif start_date:
                start_month = start_date[:7]
                query = query.gte('Month', start_month)
            elif end_date:
                end_month = end_date[:7]
                query = query.lte('Month', end_month)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)
                # Remove internal database columns
                columns_to_remove = ['id', 'site_id']
                clean_df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])
                return clean_df
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered GA data: {e}")
        return pd.DataFrame()


async def get_filtered_external_links_data(supabase_client, start_date: str = None, end_date: str = None, date_filter: str = None):
    """Get external links data with date filtering for SEO analysis"""
    try:
        # Try to use clean data method first
        try:
            if date_filter:
                return supabase_client.get_external_links_for_excel(date_filter)
            else:
                return supabase_client.get_external_links_for_excel()
        except Exception as clean_error:
            logger.warning(f"Clean external links method failed, using fallback: {clean_error}")

            # Fallback: get all data and filter manually
            query = supabase_client.client.table('internal_links').select('*').eq('site_id', supabase_client.site_id)

            if date_filter:
                query = query.eq('snapshot_date', date_filter)
            elif start_date and end_date:
                query = query.gte('snapshot_date', start_date).lte('snapshot_date', end_date)
            elif start_date:
                query = query.gte('snapshot_date', start_date)
            elif end_date:
                query = query.lte('snapshot_date', end_date)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)
                # Remove internal database columns
                columns_to_remove = ['id', 'site_id', 'link_hash', 'snapshot_date']
                clean_df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])

                # Filter to only external links
                if 'Link Type' in clean_df.columns and not clean_df.empty:
                    external_types = ['External', 'external']
                    clean_df = clean_df[clean_df['Link Type'].isin(external_types)]

                # Add domain analysis for SEO insights
                if 'Target Hyperlink' in clean_df.columns and not clean_df.empty:
                    clean_df['Target Domain'] = clean_df['Target Hyperlink'].apply(supabase_client._extract_domain)
                    clean_df['Authority Level'] = clean_df['Target Domain'].apply(supabase_client._assess_domain_authority)

                # Keep relevant columns for external links analysis
                target_columns = ['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic', 'Target Domain', 'Authority Level']
                clean_df = clean_df[[col for col in target_columns if col in clean_df.columns]]

                return clean_df
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"Error getting filtered external links data: {e}")
        return pd.DataFrame()


@router.post("/generate_report/", response_model=TaskResponse)
async def generate_report(config_data: ConfigSchema, background_tasks: BackgroundTasks):
    """
    Runs the SEO data analysis using configuration provided in the request body and generates a report.
    Uses Supabase credentials from environment variables if not provided in request.
    """
    try:
        # Convert Pydantic model to a dictionary
        config_dict = config_data.model_dump(exclude_unset=True)

        # Use environment variables as fallback for Supabase credentials
        if not config_dict.get('supabase_url') and settings.supabase_url:
            config_dict['supabase_url'] = settings.supabase_url
        if not config_dict.get('supabase_key') and settings.supabase_key:
            config_dict['supabase_key'] = settings.supabase_key
        
        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.ANALYSIS,
            config=config_dict,
            estimated_duration=3600  # 1 hour estimate
        )

        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_dict, task_id)
        
        return TaskResponse(
            task_id=task_id,
            status="running",
            message="Analysis started. Check progress with /task/{task_id}"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Configuration error: {e}")
    except Exception as e:
        logger.exception("Error running analysis:")
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {e}")


@router.post("/sites/", response_model=dict)
async def add_site(request: AddSiteSchema):
    """
    Add a new site with configuration (no analysis)
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Create site with configuration
        success, message, site_id = SupabaseClient.create_site_with_config(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain_property=request.domain_property,
            ga_property_id=request.ga_property_id,
            service_account_data=request.service_account_data,
            homepage=request.homepage,
            wp_api_key=request.wp_api_key
        )

        if success:
            from urllib.parse import urlparse
            domain = urlparse(request.domain_property).netloc

            return {
                "success": True,
                "message": message,
                "domain": domain,
                "site_id": site_id,
                "action": "site_created",
                "ready_for_analysis": True
            }
        else:
            if "already exists" in message:
                raise HTTPException(status_code=409, detail=message)
            else:
                raise HTTPException(status_code=500, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error adding site:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sites/add-and-analyze/", response_model=TaskResponse)
async def add_site_and_analyze(
    background_tasks: BackgroundTasks,
    request: AddSiteAndAnalyzeSchema
):
    """
    Add a new site with configuration and run analysis immediately
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # First create the site with configuration
        success, message, site_id = SupabaseClient.create_site_with_config(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain_property=request.domain_property,
            ga_property_id=request.ga_property_id,
            service_account_data=request.service_account_data,
            homepage=request.homepage,
            wp_api_key=request.wp_api_key
        )

        if not success:
            if "already exists" in message:
                raise HTTPException(status_code=409, detail=message)
            else:
                raise HTTPException(status_code=500, detail=message)

        # Now run the analysis
        from urllib.parse import urlparse
        domain = urlparse(request.domain_property).netloc

        # Build config for analysis
        config_data = {
            'domain_property': request.domain_property,
            'ga_property_id': request.ga_property_id,
            'supabase_url': settings.supabase_url,
            'supabase_key': settings.supabase_key,
            'homepage': request.homepage,
            'wp_api_key': request.wp_api_key,
            'start_date': request.start_date,
            'end_date': request.end_date
        }

        # Create temporary file for service account
        temp_file = create_temp_json_file(request.service_account_data)
        config_data["service_account_file"] = temp_file

        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.ANALYSIS,
            config=config_data,
            estimated_duration=3600  # 1 hour estimate
        )

        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_data, task_id)
        background_tasks.add_task(cleanup_temp_file, temp_file)

        return TaskResponse(
            task_id=task_id,
            status="running",
            message=f"Site {domain} created and analysis started. Check progress with /task/{task_id}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error adding site and running analysis:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reanalyze_site/", response_model=TaskResponse)
async def reanalyze_site(
    background_tasks: BackgroundTasks,
    request: ReAnalysisRequestSchema,
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    REANALYZE: Wipe existing data and start fresh analysis.
    Use this for a complete refresh when you want to remove old data.
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info first and verify ownership
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        site_response = client.table('sites').select('*').eq('id', request.site_id).eq('user_id', current_user['user_id']).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {request.site_id} not found or you don't have permission to access it")

        site_info = site_response.data[0]
        domain = site_info['domain']

        # Now create SupabaseClient with the correct domain
        supabase_client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain=domain
        )

        # Check if configuration exists
        if not site_info.get('domain_property') or not site_info.get('ga_property_id') or not site_info.get('service_account_data'):
            raise HTTPException(
                status_code=400,
                detail=f"Site {domain} does not have stored configuration. Please add the site with configuration first."
            )

        # Build config from stored data with WIPE mode (reanalyze = fresh start)
        config_data = {
            'domain_property': site_info['domain_property'],
            'ga_property_id': site_info['ga_property_id'],
            'supabase_url': settings.supabase_url,
            'supabase_key': settings.supabase_key,
            'homepage': site_info.get('homepage'),
            'wp_api_key': site_info.get('wp_api_key'),
            'start_date': request.start_date,
            'end_date': request.end_date,
            'wipe_existing': True  # Reanalyze = wipe and start fresh
        }

        # Create temporary file for service account
        service_account_data = site_info['service_account_data']
        temp_file = create_temp_json_file(service_account_data)
        config_data["service_account_file"] = temp_file

        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.REANALYSIS,
            site_id=site_info.get('id'),
            config=config_data,
            estimated_duration=3600  # 1 hour estimate
        )

        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_data, task_id)
        background_tasks.add_task(cleanup_temp_file, temp_file)

        return TaskResponse(
            task_id=task_id,
            status="running",
            message=f"Full re-analysis started for {domain} (wiping old data). Check progress with /task/{task_id}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error processing re-analysis request:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze_site/", response_model=TaskResponse)
async def analyze_site(
    background_tasks: BackgroundTasks,
    request: ReAnalysisRequestSchema,
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    ANALYZE: Smart incremental updates (default behavior).
    Only saves content that has changed, preventing database bloat.
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info first and verify ownership
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        site_response = client.table('sites').select('*').eq('id', request.site_id).eq('user_id', current_user['user_id']).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {request.site_id} not found or you don't have permission to access it")

        site_info = site_response.data[0]
        domain = site_info['domain']

        # Now create SupabaseClient with the correct domain
        supabase_client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain=domain
        )

        # Check if configuration exists
        if not site_info.get('domain_property') or not site_info.get('ga_property_id') or not site_info.get('service_account_data'):
            raise HTTPException(
                status_code=400,
                detail=f"Site {domain} does not have stored configuration. Please add the site with configuration first."
            )

        # Build config from stored data with INCREMENTAL mode (analyze = smart updates)
        config_data = {
            'domain_property': site_info['domain_property'],
            'ga_property_id': site_info['ga_property_id'],
            'supabase_url': settings.supabase_url,
            'supabase_key': settings.supabase_key,
            'homepage': site_info.get('homepage'),
            'wp_api_key': site_info.get('wp_api_key'),
            'start_date': request.start_date,
            'end_date': request.end_date,
            'incremental': True  # Analyze = incremental mode (default)
        }

        # Create temporary file for service account
        service_account_data = site_info['service_account_data']
        temp_file = create_temp_json_file(service_account_data)
        config_data["service_account_file"] = temp_file

        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.ANALYSIS,
            site_id=site_info.get('id'),
            config=config_data,
            estimated_duration=3600  # 1 hour estimate
        )

        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_data, task_id)
        background_tasks.add_task(cleanup_temp_file, temp_file)

        return TaskResponse(
            task_id=task_id,
            status="running",
            message=f"Analysis started for {domain} (smart updates - only changed content). Check progress with /task/{task_id}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error processing incremental re-analysis request:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate_report_with_service_account/", response_model=TaskResponse)
async def generate_report_with_service_account(
    background_tasks: BackgroundTasks,
    config_data: dict = Body(...)
):
    """
    Runs the SEO data analysis with service account data in the request body.
    Uses Supabase credentials from environment variables if not provided in request.
    Saves configuration for future re-analysis.
    """
    try:
        # Extract service account data
        service_account_data = config_data.pop("service_account_data", None)
        if not service_account_data:
            raise HTTPException(status_code=400, detail="Service account data is required")

        # Use environment variables as fallback for Supabase credentials
        if not config_data.get('supabase_url') and settings.supabase_url:
            config_data['supabase_url'] = settings.supabase_url
        if not config_data.get('supabase_key') and settings.supabase_key:
            config_data['supabase_key'] = settings.supabase_key

        # Create a temporary file for the service account
        temp_file = create_temp_json_file(service_account_data)

        # Update the config to use the temp file
        config_data["service_account_file"] = temp_file

        # Store service account data for configuration saving
        config_data["_service_account_data"] = service_account_data

        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.ANALYSIS,
            config=config_data,
            estimated_duration=3600  # 1 hour estimate
        )

        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis_with_config_save, config_data, task_id)
        background_tasks.add_task(cleanup_temp_file, temp_file)

        return TaskResponse(
            task_id=task_id,
            status="running",
            message="Analysis started. Check progress with /task/{task_id}"
        )
    except Exception as e:
        logger.exception("Error processing request with service account data:")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", response_model=TaskStatus)
async def get_task_progress_endpoint(task_id: str):
    """
    Get the current progress of a running task.
    """
    try:
        # Try to get from database first
        task = task_manager.get_task(task_id)
        if task:
            # Add debug logging for progress retrieval
            logger.debug(f"Retrieved task {task_id}: status={task.status}, progress={task.progress}, message={task.message}")
            return TaskStatus(
                status=task.status,
                progress=task.progress,
                message=task.message,
                result=task.result,
                error=task.error_message
            )

        # Fallback to old system for backward compatibility
        task_data = task_progress.get(task_id)
        if task_data:
            logger.debug(f"Retrieved task {task_id} from fallback: {task_data}")
            return TaskStatus(**task_data)

        logger.debug(f"Task {task_id} not found in database or fallback")
        return TaskStatus(
            status="not_found",
            message="Task not found or completed"
        )
    except Exception as e:
        logger.error(f"Error getting task {task_id}: {e}")
        return TaskStatus(
            status="error",
            message=f"Error retrieving task: {str(e)}"
        )


@router.post("/generate_excel_report/", response_model=TaskResponse)
async def generate_excel_report_endpoint(excel_request: ExcelRequestSchema, background_tasks: BackgroundTasks):
    """
    Generate an Excel report from Supabase data for a specific domain and date.
    """
    try:
        # Convert Pydantic model to a dictionary
        request_data = excel_request.model_dump(exclude_unset=True)
        
        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.REPORT_GENERATION,
            config=request_data,
            estimated_duration=300  # 5 minutes estimate for report generation
        )

        # Add task to background tasks
        background_tasks.add_task(generate_excel_from_supabase, request_data, task_id)
        
        return TaskResponse(
            task_id=task_id,
            status="running",
            message="Excel report generation started. Check progress with /task/{task_id}"
        )
    except Exception as e:
        logger.exception("Error generating Excel report:")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/download/{file_path:path}")
async def download_file(file_path: str):
    """
    Download a generated report file with enhanced security
    """
    import os
    from pathlib import Path

    # Security checks to prevent directory traversal and unauthorized access
    if ".." in file_path or file_path.startswith("/") or file_path.startswith("\\"):
        raise HTTPException(status_code=400, detail="Invalid file path")

    # Normalize and resolve the path
    file_path = os.path.normpath(file_path)

    # Ensure the file is within allowed directories (reports or temp)
    allowed_dirs = [
        os.path.abspath(settings.reports_dir),
        os.path.abspath(settings.temp_dir)
    ]

    absolute_file_path = os.path.abspath(file_path)

    # Check if file is within allowed directories
    is_allowed = False
    for allowed_dir in allowed_dirs:
        try:
            # Use Path.resolve() to handle symlinks and relative paths
            if Path(absolute_file_path).resolve().is_relative_to(Path(allowed_dir).resolve()):
                is_allowed = True
                break
        except (ValueError, OSError):
            continue

    if not is_allowed:
        raise HTTPException(status_code=403, detail="Access denied to this file location")

    # Check if file exists and is a regular file
    if not os.path.isfile(absolute_file_path):
        raise HTTPException(status_code=404, detail="File not found")

    # Determine media type based on file extension
    file_extension = os.path.splitext(absolute_file_path)[1].lower()
    media_type_map = {
        '.xlsx': "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        '.xls': "application/vnd.ms-excel",
        '.csv': "text/csv",
        '.json': "application/json",
        '.txt': "text/plain"
    }

    media_type = media_type_map.get(file_extension, "application/octet-stream")

    return FileResponse(
        path=absolute_file_path,
        filename=os.path.basename(absolute_file_path),
        media_type=media_type
    )


@router.get("/supabase_data/{domain}")
async def get_supabase_data_info(domain: str):
    """
    Get information about available data in Supabase for a domain
    Uses Supabase credentials from environment variables
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured in environment")

        # Initialize Supabase client using environment variables
        supabase_client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain=domain
        )

        # Get data counts efficiently (without fetching all records)
        data_counts = supabase_client.get_data_counts()

        # Get available dates and months efficiently
        dates_and_months = supabase_client.get_available_dates_and_months()

        return {
            "domain": domain,
            "site_id": supabase_client.site_id,
            "data_summary": data_counts,
            "available_dates": dates_and_months['available_dates'],
            "available_months": dates_and_months['available_months'],
            "last_updated": dates_and_months['available_dates'][-1] if dates_and_months['available_dates'] else None
        }

    except Exception as e:
        logger.exception(f"Error getting Supabase data info for domain {domain}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sites/", response_model=SitesListResponse)
async def list_sites(current_user: Dict[str, Any] = Depends(require_auth)):
    """
    Get a list of user's sites with basic data summary (optimized for speed)
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured in environment")

        # Create a temporary client to query sites
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)

        # Get user's sites only
        response = client.table('sites').select('*').eq('user_id', current_user['user_id']).execute()
        sites = response.data if response.data else []

        # ULTRA-FAST: Use pre-computed stats from sites table
        logger.info(f"Loading {len(sites)} sites with cached stats for maximum speed")

        # Build sites data using cached stats from sites table
        sites_with_data = []
        for site in sites:
            domain = site['domain']
            site_id = site['id']

            # Use cached stats from sites table (updated during analysis)
            pages_count = site.get('stats_pages', 0)
            keywords_count = site.get('stats_keywords', 0)
            total_links_count = site.get('stats_internal_links', 0)
            traffic_count = site.get('stats_traffic_records', 0)
            external_links_count = site.get('stats_external_links', 0)
            analytics_count = site.get('stats_analytics_records', 0)

            # Skip date queries for speed - these can be loaded on-demand
            available_dates = []
            available_months = []

            # Check if site has configuration for re-analysis
            configuration = None
            if site.get('domain_property') and site.get('ga_property_id'):
                configuration = {
                    "domain_property": site['domain_property'],
                    "ga_property_id": site['ga_property_id'],
                    "has_service_account": bool(site.get('service_account_data')),  # Don't expose actual data
                    "homepage": site.get('homepage')
                }

            sites_with_data.append({
                "domain": domain,
                "site_id": site_id,
                "created_at": site.get('created_at'),
                "data_summary": {
                    "pages": pages_count,
                    "keywords": keywords_count,
                    "traffic_records": traffic_count,
                    "internal_links": total_links_count,
                    "external_links": external_links_count,
                    "jump_links": 0,  # Can be added later if needed
                    "analytics_records": analytics_count,
                    "total_records": pages_count + keywords_count + total_links_count + external_links_count + traffic_count + analytics_count
                },
                "available_dates": available_dates,  # Empty for speed
                "available_months": available_months,  # Empty for speed
                "last_updated": None,  # Skip for speed
                "configuration": configuration
            })

        return {
            "sites": sites_with_data,
            "total_sites": len(sites_with_data)
        }

    except Exception as e:
        logger.exception("Error listing sites:")
        raise HTTPException(status_code=500, detail=str(e))


async def update_site_stats(site_id: str):
    """
    Update cached statistics for a site in the sites table
    Called after analysis completion to keep stats current
    """
    try:
        if not SUPABASE_AVAILABLE:
            logger.warning("Supabase not available, skipping stats update")
            return False

        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)

        # Get actual counts from data tables using Supabase SDK
        pages_count = client.table('pages').select('id', count='exact').eq('site_id', site_id).execute().count or 0
        keywords_count = client.table('gsc_keywords').select('id', count='exact').eq('site_id', site_id).execute().count or 0
        links_count = client.table('internal_links').select('id', count='exact').eq('site_id', site_id).execute().count or 0
        traffic_count = client.table('gsc_traffic').select('id', count='exact').eq('site_id', site_id).execute().count or 0

        # Count analytics data (GA4 data)
        try:
            analytics_count = client.table('ga_data').select('id', count='exact').eq('site_id', site_id).execute().count or 0
        except:
            analytics_count = 0

        # Count external links (stored in internal_links table with Link Type = 'External')
        try:
            external_links_count = client.table('internal_links').select('id', count='exact').eq('site_id', site_id).eq('Link Type', 'External').execute().count or 0
        except:
            external_links_count = 0

        # Update the sites table with cached stats using Supabase SDK
        update_data = {
            'stats_pages': pages_count,
            'stats_keywords': keywords_count,
            'stats_internal_links': links_count,
            'stats_traffic_records': traffic_count,
            'stats_external_links': external_links_count,
            'stats_analytics_records': analytics_count,
            'stats_last_updated': 'now()'
        }

        result = client.table('sites').update(update_data).eq('id', site_id).execute()
        logger.info(f"✅ Updated stats for site {site_id}: {pages_count} pages, {keywords_count} keywords, {links_count} internal links, {external_links_count} external links, {traffic_count} traffic, {analytics_count} analytics")

        return True

    except Exception as e:
        logger.exception(f"❌ Error updating site stats for {site_id}:")
        return False





@router.get("/sites/{site_id}/info", response_model=dict)
async def get_site_info(site_id: str):
    """
    Get detailed information about a specific site (for debugging)
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        site_response = client.table('sites').select('*').eq('id', site_id).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {site_id} not found")

        site_info = site_response.data[0]

        # Remove sensitive data for response
        safe_site_info = {
            "site_id": site_info.get('id'),
            "domain": site_info.get('domain'),
            "domain_property": site_info.get('domain_property'),
            "ga_property_id": site_info.get('ga_property_id'),
            "homepage": site_info.get('homepage'),
            "created_at": site_info.get('created_at'),
            "last_updated": site_info.get('last_updated'),
            "has_service_account": bool(site_info.get('service_account_data'))
        }

        return {
            "success": True,
            "site_info": safe_site_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error getting site info:")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sites/{site_id}/config", response_model=dict)
async def update_site_configuration(site_id: str, request: UpdateSiteConfigSchema):
    """
    Update configuration for an existing site
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info first
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        site_response = client.table('sites').select('*').eq('id', site_id).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {site_id} not found")

        site_info = site_response.data[0]
        domain = site_info['domain']

        # Initialize Supabase client for this site
        supabase_client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain=domain
        )

        # Handle service account data - keep existing if placeholder is sent
        service_account_data = request.service_account_data
        if isinstance(service_account_data, dict) and service_account_data.get('_keep_existing'):
            # Get existing service account data
            existing_config = supabase_client.get_site_configuration()
            if existing_config and existing_config.get('service_account_data'):
                service_account_data = existing_config['service_account_data']
            else:
                raise HTTPException(status_code=400, detail="No existing service account data found")

        # Update the configuration
        success, error_message = supabase_client.update_site_configuration(
            domain_property=request.domain_property,
            ga_property_id=request.ga_property_id,
            service_account_data=service_account_data,
            homepage=request.homepage,
            wp_api_key=request.wp_api_key
        )

        if success:
            return {
                "success": True,
                "message": f"Configuration updated successfully for {domain}",
                "domain": domain,
                "site_id": site_id
            }
        else:
            logger.error(f"Failed to update configuration for site {site_id}: {error_message}")
            raise HTTPException(status_code=500, detail=f"Failed to update configuration: {error_message}")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error updating site configuration:")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sites/{site_id}/data", response_model=dict)
async def delete_site_data(site_id: str, request: DeleteSiteDataSchema):
    """
    Delete all analysis data for a site (keeps site record and configuration intact)
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info first
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)

        # Ensure site_id is treated as integer for database query
        try:
            site_id_int = int(site_id)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid site ID format: {site_id}")

        logger.info(f"Clearing data for site_id: {site_id} (as int: {site_id_int})")
        site_response = client.table('sites').select('*').eq('id', site_id_int).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {site_id} not found")

        site_info = site_response.data[0]
        domain = site_info['domain']

        # Debug: Log the current site configuration before clearing data
        logger.info(f"Site before clearing data - ID: {site_id}, Domain: {domain}")
        logger.info(f"Current configuration: domain_property={site_info.get('domain_property')}, ga_property_id={site_info.get('ga_property_id')}")
        logger.info(f"Has service_account_data: {bool(site_info.get('service_account_data'))}")
        logger.info(f"Full site info: {site_info}")

        # Verify domain confirmation
        if request.confirm_domain.lower() != domain.lower():
            raise HTTPException(
                status_code=400,
                detail=f"Domain confirmation failed. Expected '{domain}', got '{request.confirm_domain}'"
            )

        # Create Supabase client directly with the site_id to avoid domain lookup issues
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)

        # Store the original site configuration before any operations
        original_config = {
            'domain_property': site_info.get('domain_property'),
            'ga_property_id': site_info.get('ga_property_id'),
            'service_account_data': site_info.get('service_account_data'),
            'homepage': site_info.get('homepage'),
            'wp_api_key': site_info.get('wp_api_key')
        }

        # Delete data from all tables for this specific site_id
        tables_to_clean = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
        deleted_counts = {}

        for table in tables_to_clean:
            try:
                # Get count before deletion
                count_response = client.table(table).select('id').eq('site_id', site_id_int).execute()
                count = len(count_response.data) if count_response.data else 0

                if count > 0:
                    # Delete all records for this site
                    client.table(table).delete().eq('site_id', site_id_int).execute()
                    deleted_counts[table] = count
                    logger.info(f"Deleted {count} records from {table} for site_id {site_id_int}")
                else:
                    deleted_counts[table] = 0

            except Exception as table_error:
                logger.error(f"Error deleting from table {table}: {table_error}")
                deleted_counts[table] = f"Error: {table_error}"

        # Update the sites table with explicit configuration preservation
        try:
            # Prepare update data with all original config plus updated timestamp
            update_data = {
                'last_updated': datetime.now().isoformat(),
                'domain_property': original_config['domain_property'],
                'ga_property_id': original_config['ga_property_id'],
                'service_account_data': original_config['service_account_data'],
                'homepage': original_config['homepage'],
                'wp_api_key': original_config['wp_api_key']
            }

            # Remove None values to avoid overwriting with null
            update_data = {k: v for k, v in update_data.items() if v is not None}

            update_response = client.table('sites').update(update_data).eq('id', site_id_int).execute()
            logger.info(f"Updated site {site_id_int} with explicit configuration preservation")
            logger.info(f"Update response: {update_response.data}")

            # Verify the site data after update
            verify_response = client.table('sites').select('*').eq('id', site_id_int).execute()
            if verify_response.data:
                updated_site = verify_response.data[0]
                logger.info(f"Site after update - domain_property: {updated_site.get('domain_property')}, ga_property_id: {updated_site.get('ga_property_id')}")
                logger.info(f"Has service_account_data after update: {bool(updated_site.get('service_account_data'))}")

                # Verify configuration was preserved
                config_preserved = (
                    updated_site.get('domain_property') == original_config['domain_property'] and
                    updated_site.get('ga_property_id') == original_config['ga_property_id'] and
                    bool(updated_site.get('service_account_data')) == bool(original_config['service_account_data'])
                )

                if config_preserved:
                    logger.info(f"✅ Configuration successfully preserved for site {site_id_int}")
                else:
                    logger.error(f"❌ Configuration NOT preserved for site {site_id_int}")
            else:
                logger.error(f"Could not verify site data after update - site not found!")

        except Exception as update_error:
            logger.error(f"Error updating site: {update_error}")

        logger.info(f"Site data deletion summary for site_id {site_id_int}: {deleted_counts}")
        success = True

        if success:
            return {
                "success": True,
                "message": f"All data deleted successfully for {domain}",
                "domain": domain,
                "site_id": site_id,
                "action": "data_cleared"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to delete site data")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error deleting site data:")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sites/{site_id}", response_model=dict)
async def delete_site_completely(site_id: str, request: DeleteSiteDataSchema):
    """
    Delete site completely (removes site record and all data)
    """
    try:
        # Check if Supabase is available and configured
        if not SUPABASE_AVAILABLE:
            raise HTTPException(status_code=500, detail="Supabase client not available")

        if not settings.supabase_url or not settings.supabase_key:
            raise HTTPException(status_code=500, detail="Supabase credentials not configured")

        # Get site info first
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        site_response = client.table('sites').select('*').eq('id', site_id).execute()

        if not site_response.data:
            raise HTTPException(status_code=404, detail=f"Site with ID {site_id} not found")

        site_info = site_response.data[0]
        domain = site_info['domain']

        # Verify domain confirmation
        if request.confirm_domain.lower() != domain.lower():
            raise HTTPException(
                status_code=400,
                detail=f"Domain confirmation failed. Expected '{domain}', got '{request.confirm_domain}'"
            )

        # Initialize Supabase client for this site
        supabase_client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain=domain
        )

        # Delete site completely
        logger.info(f"Attempting to delete site {domain} completely (ID: {site_id})")

        try:
            success = supabase_client.delete_site_completely()

            if success:
                logger.info(f"✅ Successfully deleted site {domain} completely")
                return {
                    "success": True,
                    "message": f"Site {domain} deleted completely",
                    "domain": domain,
                    "site_id": site_id,
                    "action": "site_deleted"
                }
            else:
                error_msg = f"Failed to delete site {domain} completely. Check logs for details."
                logger.error(f"❌ {error_msg}")
                raise HTTPException(status_code=500, detail=error_msg)

        except Exception as deletion_error:
            # Import the custom exceptions
            from src.database.supabase_client import SiteDeletionError, ForeignKeyConstraintError

            if isinstance(deletion_error, SiteDeletionError):
                logger.warning(f"⚠️ Normal deletion failed, attempting force deletion for site {domain}")

                # Try force deletion as fallback
                try:
                    force_success = supabase_client.force_delete_site()
                    if force_success:
                        logger.info(f"✅ Successfully force deleted site {domain}")
                        return {
                            "success": True,
                            "message": f"Site {domain} deleted completely (force deletion used)",
                            "domain": domain,
                            "site_id": site_id,
                            "action": "site_deleted"
                        }
                    else:
                        error_msg = (
                            f"Cannot delete site {domain} even with force deletion. "
                            f"Remaining data: {deletion_error.remaining_refs}. "
                            f"Manual database cleanup may be required."
                        )
                        logger.error(f"❌ Force deletion also failed: {error_msg}")
                        raise HTTPException(status_code=409, detail=error_msg)

                except Exception as force_error:
                    error_msg = (
                        f"Both normal and force deletion failed for site {domain}. "
                        f"Error: {str(force_error)}. Manual database cleanup required."
                    )
                    logger.error(f"❌ Force deletion error: {error_msg}")
                    raise HTTPException(status_code=500, detail=error_msg)
            else:
                error_msg = f"Unexpected error deleting site {domain}: {str(deletion_error)}"
                logger.error(f"❌ {error_msg}")
                raise HTTPException(status_code=500, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error deleting site completely:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate_excel_enhanced/", response_model=TaskResponse)
async def generate_excel_enhanced(excel_request: EnhancedExcelRequestSchema, background_tasks: BackgroundTasks):
    """
    Enhanced Excel report generation with site selection and date range filtering
    """
    try:
        # Convert Pydantic model to a dictionary
        request_data = excel_request.model_dump(exclude_unset=True)

        # Determine domain from site_id if needed
        if request_data.get('site_id') and not request_data.get('domain'):
            # Query the site to get the domain
            if not SUPABASE_AVAILABLE or not settings.supabase_url or not settings.supabase_key:
                raise HTTPException(status_code=500, detail="Supabase not configured")

            from supabase import create_client
            client = create_client(settings.supabase_url, settings.supabase_key)
            site_response = client.table('sites').select('domain').eq('id', request_data['site_id']).execute()

            if not site_response.data:
                raise HTTPException(status_code=404, detail=f"Site with ID {request_data['site_id']} not found")

            request_data['domain'] = site_response.data[0]['domain']

        # Create task in database
        task_id = task_manager.create_task(
            task_type=TaskType.REPORT_GENERATION,
            site_id=request_data.get('site_id'),
            config=request_data,
            estimated_duration=300  # 5 minutes estimate for report generation
        )

        # Add task to background tasks
        background_tasks.add_task(generate_excel_from_supabase_enhanced, request_data, task_id)

        return TaskResponse(
            task_id=task_id,
            status="running",
            message="Enhanced Excel report generation started. Check progress with /task/{task_id}"
        )
    except Exception as e:
        logger.exception("Error generating enhanced Excel report:")
        raise HTTPException(status_code=500, detail=str(e))


@router.options("/generate_report/")
async def options_generate_report():
    """
    Handle CORS preflight requests for the generate_report endpoint
    """
    return Response(status_code=200)


# ============================================================================
# TASK MANAGEMENT ENDPOINTS
# ============================================================================

@router.get("/tasks/", response_model=TaskListResponse)
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    site_id: Optional[int] = None,
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    List tasks with optional filtering
    """
    try:
        from src.services.task_manager import TaskStatus as TaskStatusEnum

        status_filter = None
        if status:
            try:
                status_filter = TaskStatusEnum(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        tasks = task_manager.list_tasks(
            user_id=current_user["user_id"],
            status=status_filter,
            site_id=site_id,
            limit=limit,
            offset=offset
        )

        task_details = []
        for task in tasks:
            task_details.append(TaskDetail(
                id=task.id,
                site_id=task.site_id,
                task_type=task.task_type,
                status=task.status,
                priority=task.priority,
                progress=task.progress,
                message=task.message,
                config=task.config,
                result=task.result,
                error_message=task.error_message,
                created_at=task.created_at.isoformat(),
                started_at=task.started_at.isoformat() if task.started_at else None,
                completed_at=task.completed_at.isoformat() if task.completed_at else None,
                estimated_duration=task.estimated_duration,
                actual_duration=task.actual_duration,
                retry_count=task.retry_count,
                max_retries=task.max_retries,
                user_session=task.user_session
            ))

        return TaskListResponse(
            tasks=task_details,
            total_count=len(task_details),  # TODO: Add proper count query
            page=offset // limit + 1,
            limit=limit
        )

    except Exception as e:
        logger.error(f"Error listing tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskDetail)
async def get_task_detail(
    task_id: str,
    current_user: Dict[str, Any] = Depends(RequireTaskOwnership())
):
    """
    Get detailed information about a specific task
    """
    try:
        task = task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return TaskDetail(
            id=task.id,
            site_id=task.site_id,
            task_type=task.task_type,
            status=task.status,
            priority=task.priority,
            progress=task.progress,
            message=task.message,
            config=task.config,
            result=task.result,
            error_message=task.error_message,
            created_at=task.created_at.isoformat(),
            started_at=task.started_at.isoformat() if task.started_at else None,
            completed_at=task.completed_at.isoformat() if task.completed_at else None,
            estimated_duration=task.estimated_duration,
            actual_duration=task.actual_duration,
            retry_count=task.retry_count,
            max_retries=task.max_retries,
            user_session=task.user_session
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    current_user: Dict[str, Any] = Depends(RequireTaskOwnership())
):
    """
    Cancel a running task
    """
    try:
        success = task_manager.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found or already completed")

        return {"message": f"Task {task_id} cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/logs", response_model=TaskLogsResponse)
async def get_task_logs(
    task_id: str,
    limit: int = 100,
    current_user: Dict[str, Any] = Depends(RequireTaskOwnership())
):
    """
    Get logs for a specific task
    """
    try:
        # Verify task exists
        task = task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        logs_data = task_manager.get_task_logs(task_id, limit)

        logs = []
        for log_data in logs_data:
            logs.append(TaskLogEntry(
                id=log_data["id"],
                level=log_data["level"],
                message=log_data["message"],
                details=log_data["details"],
                created_at=log_data["created_at"]
            ))

        return TaskLogsResponse(
            task_id=task_id,
            logs=logs
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting logs for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-progress/{task_id}")
async def test_progress_updates(task_id: str):
    """
    Debug endpoint to test progress updates for a task
    """
    try:
        import asyncio

        logger.info(f"🧪 DEBUG: Testing progress updates for task {task_id}")

        # Check if task exists, create if it doesn't
        existing_task = task_manager.get_task(task_id)
        if not existing_task:
            logger.info(f"🔧 DEBUG: Task {task_id} doesn't exist, creating test task")
            # Create a test task with the specific ID
            task_data = {
                "id": task_id,
                "task_type": TaskType.ANALYSIS.value,
                "status": "running",
                "priority": 5,
                "progress": 0,
                "message": "Test task created",
                "config": {"test": True},
                "retry_count": 0,
                "max_retries": 3,
                "created_at": datetime.now().isoformat()
            }

            # Insert directly into database
            task_manager.supabase.table("tasks").insert(task_data).execute()
            logger.info(f"✅ DEBUG: Test task {task_id} created")
        else:
            logger.info(f"📋 DEBUG: Task {task_id} already exists with status: {existing_task.status}")

        # Simulate progress updates with delays
        progress_steps = [10, 25, 50, 75, 90, 100]
        for i, progress in enumerate(progress_steps):
            logger.info(f"📊 DEBUG: Updating task {task_id} to {progress}%")
            update_task_progress(task_id, progress, f"Test progress update: {progress}%")

            # Wait between updates to make them visible
            if i < len(progress_steps) - 1:  # Don't wait after the last update
                await asyncio.sleep(2)  # Wait 2 seconds between updates

        logger.info(f"🎉 DEBUG: Test progress updates completed for task {task_id}")
        return {"message": f"Test progress updates completed for task {task_id}", "steps": progress_steps}

    except Exception as e:
        logger.error(f"❌ DEBUG: Error in test progress updates: {e}")
        logger.exception("Full exception details:")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/create-test-task")
async def create_test_task():
    """
    Create a test task for debugging progress updates
    """
    try:
        import uuid

        # Create a test task
        task_id = task_manager.create_task(
            task_type=TaskType.ANALYSIS,
            config={"test": True, "debug": True},
            estimated_duration=60
        )

        logger.info(f"🧪 DEBUG: Created test task {task_id}")

        return {
            "message": "Test task created successfully",
            "task_id": task_id,
            "instructions": f"Use testProgressWithTaskId('{task_id}') in the browser console to test progress updates"
        }

    except Exception as e:
        logger.error(f"❌ DEBUG: Error creating test task: {e}")
        raise HTTPException(status_code=500, detail=str(e))



