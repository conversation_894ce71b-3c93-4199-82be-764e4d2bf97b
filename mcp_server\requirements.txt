# MCP Server Requirements for SEO Analytics

# Core MCP dependencies
mcp>=1.0.0

# Project dependencies (inherit from main project)
fastapi>=0.104.1
uvicorn>=0.24.0
supabase>=2.0.0
pydantic>=2.5.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
pandas>=2.1.0
openpyxl>=3.1.0
requests>=2.31.0
beautifulsoup4>=4.12.0
google-api-python-client>=2.100.0
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
python-dateutil>=2.8.2

# Additional dependencies for MCP server
asyncio-mqtt>=0.13.0
websockets>=12.0
