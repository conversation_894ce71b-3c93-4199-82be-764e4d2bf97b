# SEO Analytics MCP Server Deployment Guide

## Prerequisites

1. **Python Environment**
   - Python 3.8 or higher
   - Virtual environment (recommended)

2. **Environment Variables**
   ```bash
   export SUPABASE_URL="your-supabase-project-url"
   export SUPABASE_KEY="your-supabase-anon-key"
   export JWT_SECRET_KEY="your-jwt-secret-key"
   ```

3. **Database Setup**
   - Supabase project with the SEO analytics schema
   - User authentication tables configured
   - Row-level security policies enabled

## Installation

### 1. Install Dependencies

```bash
# From the project root
pip install -r mcp_server/requirements.txt
```

### 2. Verify Installation

```bash
# Test the server can start
python -m mcp_server.start_server --help
```

## Configuration

### 1. MCP Client Configuration

Add to your MCP client configuration file (e.g., <PERSON>):

```json
{
  "mcpServers": {
    "seo-analytics": {
      "command": "python",
      "args": ["-m", "mcp_server.start_server"],
      "env": {
        "SUPABASE_URL": "${SUPABASE_URL}",
        "SUPABASE_KEY": "${SUPABASE_KEY}",
        "JWT_SECRET_KEY": "${JWT_SECRET_KEY}"
      }
    }
  }
}
```

### 2. Environment Setup

Create a `.env` file in your project root:

```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
```

## Running the Server

### 1. Standalone Mode

```bash
# Set environment variables
export SUPABASE_URL="your-url"
export SUPABASE_KEY="your-key"
export JWT_SECRET_KEY="your-secret"

# Run the server
python mcp_server/start_server.py
```

### 2. Module Mode

```bash
python -m mcp_server
```

### 3. Development Mode

```bash
# With debug logging
export LOG_LEVEL=DEBUG
python mcp_server/start_server.py
```

## Testing

### 1. Authentication Test

First, get an authentication token from your main API:

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email_or_username": "<EMAIL>", "password": "password"}'
```

### 2. MCP Tool Test

Use an MCP client to test tools:

```json
{
  "tool": "list_user_sites",
  "arguments": {
    "auth_token": "your-jwt-token"
  }
}
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure project root is in Python path
   export PYTHONPATH="${PYTHONPATH}:/path/to/your/project"
   ```

2. **Authentication Failures**
   - Verify JWT_SECRET_KEY matches your main API
   - Check token expiration
   - Ensure user exists in database

3. **Database Connection Issues**
   - Verify SUPABASE_URL and SUPABASE_KEY
   - Check network connectivity
   - Ensure database schema is up to date

4. **Permission Errors**
   - Verify row-level security policies
   - Check user_id associations
   - Ensure sites belong to authenticated user

### Debug Mode

Enable detailed logging:

```bash
export LOG_LEVEL=DEBUG
python mcp_server/start_server.py
```

### Health Check

The server logs initialization status. Look for:
```
INFO - SEO Analytics MCP Server initialized successfully
```

## Security Considerations

1. **Environment Variables**
   - Never commit secrets to version control
   - Use secure environment variable management
   - Rotate keys regularly

2. **Database Security**
   - Enable row-level security (RLS)
   - Use least-privilege access
   - Monitor access logs

3. **Network Security**
   - Use HTTPS for Supabase connections
   - Implement rate limiting if needed
   - Monitor for unusual access patterns

## Monitoring

### Logs

The server logs important events:
- Authentication attempts
- Tool executions
- Database queries
- Error conditions

### Metrics

Monitor these key metrics:
- Authentication success/failure rates
- Tool execution times
- Database query performance
- Error rates by tool

## Scaling

### Performance Optimization

1. **Database Optimization**
   - Add indexes for frequently queried columns
   - Use pagination for large datasets
   - Implement query caching

2. **Connection Pooling**
   - Configure Supabase connection pooling
   - Implement connection retry logic
   - Monitor connection usage

### High Availability

1. **Multiple Instances**
   - Run multiple server instances
   - Use load balancing
   - Implement health checks

2. **Database Failover**
   - Configure Supabase backup/restore
   - Implement read replicas
   - Monitor database health

## Maintenance

### Regular Tasks

1. **Update Dependencies**
   ```bash
   pip install -r mcp_server/requirements.txt --upgrade
   ```

2. **Database Maintenance**
   - Clean up old data snapshots
   - Optimize database performance
   - Update statistics

3. **Security Updates**
   - Rotate JWT secrets
   - Update access policies
   - Review user permissions

### Backup Strategy

1. **Database Backups**
   - Automated Supabase backups
   - Regular export of critical data
   - Test restore procedures

2. **Configuration Backups**
   - Version control all configuration
   - Document environment setup
   - Maintain deployment scripts
