"""
Authentication middleware and dependencies for FastAPI
"""
from typing import Dict, Any, Optional
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src.services.auth_service import auth_service
from src.utils.logging import get_logger

logger = get_logger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


class AuthenticationRequired:
    """Dependency class for requiring authentication"""
    
    def __init__(self, require_verified: bool = False, allowed_roles: Optional[list] = None):
        self.require_verified = require_verified
        self.allowed_roles = allowed_roles or []
    
    async def __call__(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
        """
        Dependency that requires valid authentication
        """
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        try:
            user = await auth_service.get_user_by_token(credentials.credentials)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Check if email verification is required
            if self.require_verified and not user.get("is_verified", False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Email verification required"
                )
            
            # Check role permissions
            if self.allowed_roles and user.get("role") not in self.allowed_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed",
                headers={"WWW-Authenticate": "Bearer"},
            )


class OptionalAuthentication:
    """Dependency class for optional authentication"""
    
    async def __call__(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Optional[Dict[str, Any]]:
        """
        Dependency that provides optional authentication
        Returns user data if authenticated, None if not
        """
        if not credentials:
            return None
        
        try:
            user = await auth_service.get_user_by_token(credentials.credentials)
            return user
            
        except Exception as e:
            logger.debug(f"Optional authentication failed: {e}")
            return None


# ============================================================================
# COMMON DEPENDENCY INSTANCES
# ============================================================================

# Require any authenticated user
require_auth = AuthenticationRequired()

# Require verified user
require_verified_user = AuthenticationRequired(require_verified=True)

# Require admin user
require_admin = AuthenticationRequired(allowed_roles=["admin"])

# Require admin or user role
require_user_or_admin = AuthenticationRequired(allowed_roles=["user", "admin"])

# Optional authentication
optional_auth = OptionalAuthentication()


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Get current authenticated user (backward compatibility)
    """
    return await require_auth(credentials)


async def get_current_user_optional(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Optional[Dict[str, Any]]:
    """
    Get current user if authenticated, None otherwise
    """
    return await optional_auth(credentials)


def get_user_sites_filter(user: Dict[str, Any]) -> str:
    """
    Get Supabase filter for user's sites
    """
    return f"user_id.eq.{user['user_id']}"


def get_user_tasks_filter(user: Dict[str, Any]) -> str:
    """
    Get Supabase filter for user's tasks (through sites)
    """
    # This will need to be a join query or subquery
    # For now, we'll handle this in the service layer
    return f"sites.user_id.eq.{user['user_id']}"


async def check_site_ownership(site_id: int, user: Dict[str, Any]) -> bool:
    """
    Check if user owns the specified site
    """
    try:
        from src.services.auth_service import auth_service
        
        site_result = auth_service.supabase.table("sites").select("user_id").eq("id", site_id).execute()
        
        if not site_result.data:
            return False
        
        site = site_result.data[0]
        return site["user_id"] == user["user_id"]
        
    except Exception as e:
        logger.error(f"Error checking site ownership: {e}")
        return False


async def check_task_ownership(task_id: str, user: Dict[str, Any]) -> bool:
    """
    Check if user owns the task (through site ownership)
    """
    try:
        from src.services.auth_service import auth_service
        
        # Get task with site information
        task_result = auth_service.supabase.table("tasks").select("site_id").eq("id", task_id).execute()
        
        if not task_result.data:
            return False
        
        task = task_result.data[0]
        
        if not task["site_id"]:
            return False
        
        # Check site ownership
        return await check_site_ownership(task["site_id"], user)
        
    except Exception as e:
        logger.error(f"Error checking task ownership: {e}")
        return False


class RequireSiteOwnership:
    """Dependency to require site ownership"""
    
    def __init__(self, site_id_param: str = "site_id"):
        self.site_id_param = site_id_param
    
    async def __call__(self, site_id: int, user: Dict[str, Any] = Depends(require_auth)) -> Dict[str, Any]:
        """
        Require that the current user owns the specified site
        """
        if not await check_site_ownership(site_id, user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this site"
            )
        
        return user


class RequireTaskOwnership:
    """Dependency to require task ownership"""
    
    def __init__(self, task_id_param: str = "task_id"):
        self.task_id_param = task_id_param
    
    async def __call__(self, task_id: str, user: Dict[str, Any] = Depends(require_auth)) -> Dict[str, Any]:
        """
        Require that the current user owns the specified task
        """
        if not await check_task_ownership(task_id, user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this task"
            )
        
        return user


# ============================================================================
# ROLE-BASED ACCESS CONTROL
# ============================================================================

def require_role(role: str):
    """
    Create a dependency that requires a specific role
    """
    return AuthenticationRequired(allowed_roles=[role])


def require_any_role(*roles: str):
    """
    Create a dependency that requires any of the specified roles
    """
    return AuthenticationRequired(allowed_roles=list(roles))


# Common role dependencies
require_admin_role = require_role("admin")
require_user_role = require_role("user")
require_viewer_role = require_role("viewer")
