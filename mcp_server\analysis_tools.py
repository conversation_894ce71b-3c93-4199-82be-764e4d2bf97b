"""
Analysis and Reporting Tools for SEO Analytics MCP Server
"""

import json
import logging
import uuid
from typing import Any, Dict
from datetime import datetime, timezone

from mcp.types import Call<PERSON><PERSON><PERSON><PERSON>ult, TextContent, Tool

logger = logging.getLogger(__name__)

def get_analysis_tools() -> list[Tool]:
    """Get analysis and reporting tool definitions"""
    return [
        Tool(
            name="start_site_analysis",
            description="Start a new analysis for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to analyze"
                    },
                    "analysis_type": {
                        "type": "string",
                        "enum": ["full", "incremental"],
                        "description": "Type of analysis (full or incremental)"
                    },
                    "start_date": {
                        "type": "string",
                        "description": "Start date for analysis (YYYY-MM-DD)"
                    },
                    "end_date": {
                        "type": "string",
                        "description": "End date for analysis (YYYY-MM-DD)"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="get_task_status",
            description="Get the status of an analysis task",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "task_id": {
                        "type": "string",
                        "description": "Task ID to check"
                    }
                },
                "required": ["auth_token", "task_id"]
            }
        ),
        Tool(
            name="get_task_logs",
            description="Get detailed logs for an analysis task",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "task_id": {
                        "type": "string",
                        "description": "Task ID to get logs for"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of log entries to return (default: 100)"
                    }
                },
                "required": ["auth_token", "task_id"]
            }
        ),
        Tool(
            name="list_user_tasks",
            description="List all tasks for the authenticated user",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "status": {
                        "type": "string",
                        "enum": ["pending", "running", "completed", "failed"],
                        "description": "Filter by task status"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of tasks to return (default: 50)"
                    }
                },
                "required": ["auth_token"]
            }
        ),
        Tool(
            name="generate_excel_report",
            description="Generate an Excel report for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to generate report for"
                    },
                    "start_date": {
                        "type": "string",
                        "description": "Start date for report data (YYYY-MM-DD)"
                    },
                    "end_date": {
                        "type": "string",
                        "description": "End date for report data (YYYY-MM-DD)"
                    },
                    "include_sheets": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "enum": ["pages", "keywords", "traffic", "internal_links", "analytics"]
                        },
                        "description": "Which sheets to include in the report"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="get_internal_links",
            description="Retrieve internal links data for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of records to return (default: 100, max: 1000)"
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Number of records to skip (default: 0)"
                    },
                    "source_url": {
                        "type": "string",
                        "description": "Filter by source URL"
                    },
                    "target_url": {
                        "type": "string",
                        "description": "Filter by target URL"
                    },
                    "link_type": {
                        "type": "string",
                        "description": "Filter by link type"
                    },
                    "snapshot_date": {
                        "type": "string",
                        "description": "Filter by snapshot date (YYYY-MM-DD)"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="get_analytics_data",
            description="Retrieve Google Analytics data for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of records to return (default: 100, max: 1000)"
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Number of records to skip (default: 0)"
                    },
                    "month": {
                        "type": "string",
                        "description": "Filter by month (YYYY-MM)"
                    },
                    "url": {
                        "type": "string",
                        "description": "Filter by URL"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="get_monthly_trends",
            description="Get monthly traffic trends for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID"
                    },
                    "months": {
                        "type": "integer",
                        "description": "Number of months to include (default: 12)"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        )
    ]


async def handle_analysis_tool(tool_name: str, user: Dict[str, Any], args: Dict[str, Any], 
                              auth_service, settings) -> CallToolResult:
    """Handle analysis and reporting tool calls"""
    
    if tool_name == "start_site_analysis":
        return await _handle_start_site_analysis(user, args, auth_service, settings)
    elif tool_name == "get_task_status":
        return await _handle_get_task_status(user, args, auth_service, settings)
    elif tool_name == "get_task_logs":
        return await _handle_get_task_logs(user, args, auth_service, settings)
    elif tool_name == "list_user_tasks":
        return await _handle_list_user_tasks(user, args, auth_service, settings)
    elif tool_name == "generate_excel_report":
        return await _handle_generate_excel_report(user, args, auth_service, settings)
    elif tool_name == "get_internal_links":
        return await _handle_get_internal_links(user, args, auth_service, settings)
    elif tool_name == "get_analytics_data":
        return await _handle_get_analytics_data(user, args, auth_service, settings)
    elif tool_name == "get_monthly_trends":
        return await _handle_get_monthly_trends(user, args, auth_service, settings)
    else:
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: Unknown analysis tool '{tool_name}'")]
        )


async def _verify_site_ownership(user: Dict[str, Any], site_id: str, settings) -> bool:
    """Verify that user owns the specified site"""
    try:
        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )
        result = client.supabase.table("sites").select("id").eq("id", site_id).eq("user_id", user["user_id"]).execute()
        return len(result.data) > 0
    except Exception as e:
        logger.error(f"Error verifying site ownership: {e}")
        return False


async def _handle_start_site_analysis(user: Dict[str, Any], args: Dict[str, Any], 
                                    auth_service, settings) -> CallToolResult:
    """Handle start_site_analysis tool call"""
    try:
        site_id = args.get("site_id")
        if not site_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id is required")]
            )
        
        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )
        
        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )
        
        # Get site details
        site_result = client.supabase.table("sites").select("*").eq("id", site_id).execute()
        if not site_result.data:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found")]
            )
        
        site = site_result.data[0]
        
        # Create task
        task_id = str(uuid.uuid4())
        analysis_type = args.get("analysis_type", "incremental")
        
        task_data = {
            "id": task_id,
            "site_id": site_id,
            "task_type": "site_analysis",
            "status": "pending",
            "priority": 1,
            "progress": 0,
            "message": f"Starting {analysis_type} analysis for {site['domain']}",
            "config": {
                "analysis_type": analysis_type,
                "start_date": args.get("start_date"),
                "end_date": args.get("end_date"),
                "domain": site["domain"],
                "domain_property": site.get("domain_property"),
                "ga_property_id": site.get("ga_property_id"),
                "user_id": user["user_id"]
            },
            "created_at": datetime.now(timezone.utc).isoformat(),
            "retry_count": 0,
            "max_retries": 3
        }
        
        # Insert task
        result = client.supabase.table("tasks").insert(task_data).execute()
        
        if result.data:
            response = {
                "success": True,
                "task_id": task_id,
                "status": "pending",
                "message": f"{analysis_type.title()} analysis queued for {site['domain']}",
                "site_id": site_id,
                "analysis_type": analysis_type
            }
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Failed to create analysis task")]
            )
            
    except Exception as e:
        logger.error(f"Error starting site analysis: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_get_task_status(user: Dict[str, Any], args: Dict[str, Any],
                                auth_service, settings) -> CallToolResult:
    """Handle get_task_status tool call"""
    try:
        task_id = args.get("task_id")
        if not task_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: task_id is required")]
            )

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Get task with site ownership verification
        result = client.supabase.table("tasks").select(
            "*, sites!inner(domain, user_id)"
        ).eq("id", task_id).eq("sites.user_id", user["user_id"]).execute()

        if not result.data:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Task not found or access denied")]
            )

        task = result.data[0]

        response = {
            "task_id": task["id"],
            "site_id": str(task["site_id"]),
            "domain": task["sites"]["domain"],
            "task_type": task["task_type"],
            "status": task["status"],
            "progress": task.get("progress", 0),
            "message": task.get("message", ""),
            "created_at": task["created_at"],
            "started_at": task.get("started_at"),
            "completed_at": task.get("completed_at"),
            "error_message": task.get("error_message"),
            "estimated_duration": task.get("estimated_duration"),
            "actual_duration": task.get("actual_duration"),
            "retry_count": task.get("retry_count", 0),
            "max_retries": task.get("max_retries", 3)
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_get_task_logs(user: Dict[str, Any], args: Dict[str, Any],
                              auth_service, settings) -> CallToolResult:
    """Handle get_task_logs tool call"""
    try:
        task_id = args.get("task_id")
        if not task_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: task_id is required")]
            )

        limit = min(args.get("limit", 100), 500)  # Max 500 log entries

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Verify task ownership first
        task_result = client.supabase.table("tasks").select(
            "id, sites!inner(user_id)"
        ).eq("id", task_id).eq("sites.user_id", user["user_id"]).execute()

        if not task_result.data:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Task not found or access denied")]
            )

        # Get task logs
        logs_result = client.supabase.table("task_logs").select(
            "id, level, message, details, created_at"
        ).eq("task_id", task_id).order("created_at", desc=True).limit(limit).execute()

        response = {
            "task_id": task_id,
            "logs": logs_result.data,
            "total_logs": len(logs_result.data)
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error getting task logs: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_list_user_tasks(user: Dict[str, Any], args: Dict[str, Any],
                                auth_service, settings) -> CallToolResult:
    """Handle list_user_tasks tool call"""
    try:
        status = args.get("status")
        limit = min(args.get("limit", 50), 200)  # Max 200 tasks

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Build query with site ownership verification
        query = client.supabase.table("tasks").select(
            "id, site_id, task_type, status, progress, message, created_at, "
            "started_at, completed_at, error_message, sites!inner(domain, user_id)"
        ).eq("sites.user_id", user["user_id"])

        if status:
            query = query.eq("status", status)

        query = query.order("created_at", desc=True).limit(limit)

        result = query.execute()

        tasks = []
        for task in result.data:
            task_info = {
                "task_id": task["id"],
                "site_id": str(task["site_id"]),
                "domain": task["sites"]["domain"],
                "task_type": task["task_type"],
                "status": task["status"],
                "progress": task.get("progress", 0),
                "message": task.get("message", ""),
                "created_at": task["created_at"],
                "started_at": task.get("started_at"),
                "completed_at": task.get("completed_at"),
                "error_message": task.get("error_message")
            }
            tasks.append(task_info)

        response = {
            "tasks": tasks,
            "total_tasks": len(tasks),
            "filters": {
                "status": status,
                "limit": limit
            }
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error listing user tasks: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_generate_excel_report(user: Dict[str, Any], args: Dict[str, Any],
                                       auth_service, settings) -> CallToolResult:
    """Handle generate_excel_report tool call"""
    try:
        site_id = args.get("site_id")
        if not site_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id is required")]
            )

        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )

        # This would typically trigger the report generation service
        # For now, return a task ID that can be monitored
        task_id = str(uuid.uuid4())

        response = {
            "success": True,
            "task_id": task_id,
            "message": "Excel report generation started",
            "site_id": site_id,
            "parameters": {
                "start_date": args.get("start_date"),
                "end_date": args.get("end_date"),
                "include_sheets": args.get("include_sheets", ["pages", "keywords", "traffic"])
            }
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error generating Excel report: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_get_internal_links(user: Dict[str, Any], args: Dict[str, Any],
                                   auth_service, settings) -> CallToolResult:
    """Handle get_internal_links tool call"""
    try:
        site_id = args.get("site_id")
        if not site_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id is required")]
            )

        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Parse parameters
        limit = min(args.get("limit", 100), 1000)
        offset = args.get("offset", 0)
        source_url = args.get("source_url")
        target_url = args.get("target_url")
        link_type = args.get("link_type")
        snapshot_date = args.get("snapshot_date")

        # Build query
        query = client.supabase.table("internal_links").select(
            "id, URL, \"Target Hyperlink\", \"Anchor Text\", \"Link Type\", "
            "\"URL Topic\", \"Target Title\", \"Relevance Score\", \"Link Count\", snapshot_date"
        ).eq("site_id", site_id)

        # Apply filters
        if source_url:
            query = query.ilike("URL", f"%{source_url}%")
        if target_url:
            query = query.ilike("\"Target Hyperlink\"", f"%{target_url}%")
        if link_type:
            query = query.eq("\"Link Type\"", link_type)
        if snapshot_date:
            query = query.eq("snapshot_date", snapshot_date)

        # Apply pagination and ordering
        query = query.order("\"Link Count\"", desc=True).range(offset, offset + limit - 1)

        result = query.execute()

        # Get total count
        count_query = client.supabase.table("internal_links").select("id", count="exact").eq("site_id", site_id)
        if source_url:
            count_query = count_query.ilike("URL", f"%{source_url}%")
        if target_url:
            count_query = count_query.ilike("\"Target Hyperlink\"", f"%{target_url}%")
        if link_type:
            count_query = count_query.eq("\"Link Type\"", link_type)
        if snapshot_date:
            count_query = count_query.eq("snapshot_date", snapshot_date)

        count_result = count_query.execute()
        total_count = count_result.count

        response = {
            "internal_links": result.data,
            "pagination": {
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            },
            "filters": {
                "source_url": source_url,
                "target_url": target_url,
                "link_type": link_type,
                "snapshot_date": snapshot_date
            }
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error getting internal links: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )
