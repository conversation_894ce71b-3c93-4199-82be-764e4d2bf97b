# SEO Analysis Tool - API Documentation

## Overview
The SEO Analysis Tool now supports multi-user authentication, task queue management, and user isolation. All task operations require authentication.

## Base URL
```
http://localhost:8000
```

## Authentication

### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "username",
  "password": "Password123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. Please verify your email.",
  "user_id": "uuid"
}
```

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email_or_username": "<EMAIL>",
  "password": "Password123!"
}
```

**Response:**
```json
{
  "access_token": "jwt_token",
  "refresh_token": "refresh_token",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "username": "username",
    "role": "user"
  }
}
```

### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "refresh_token"
}
```

### Logout
```http
POST /auth/logout
Authorization: Bearer {access_token}
```

### Get User Profile
```http
GET /auth/me
Authorization: Bearer {access_token}
```

## Task Management

### List Tasks (User's Tasks Only)
```http
GET /tasks/?status=running&limit=50&offset=0&site_id=1
Authorization: Bearer {access_token}
```

**Response:**
```json
{
  "tasks": [
    {
      "id": "task_uuid",
      "site_id": 1,
      "task_type": "analysis",
      "status": "running",
      "priority": 2,
      "progress": 50,
      "created_at": "2024-01-01T00:00:00Z",
      "estimated_duration": 300
    }
  ],
  "total_count": 1,
  "has_more": false
}
```

### Get Task Details
```http
GET /tasks/{task_id}
Authorization: Bearer {access_token}
```

### Get Task Logs
```http
GET /tasks/{task_id}/logs?limit=100
Authorization: Bearer {access_token}
```

### Cancel Task
```http
DELETE /tasks/{task_id}
Authorization: Bearer {access_token}
```

## Queue Management

### Get Queue Status
```http
GET /queue/status
Authorization: Bearer {access_token}
```

**Response:**
```json
{
  "queue_size": 5,
  "retry_queue_size": 2,
  "running_tasks": 3,
  "max_concurrent": 5,
  "available_slots": 2,
  "is_running": true
}
```

### Get User Queue Status
```http
GET /queue/status/user
Authorization: Bearer {access_token}
```

**Response:**
```json
{
  "user_id": "uuid",
  "running_tasks": 1,
  "queued_tasks": 2,
  "total_active": 3
}
```

### Update Task Priority
```http
PUT /queue/tasks/{task_id}/priority
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "priority": "HIGH"
}
```

**Priority Levels:** `LOW`, `NORMAL`, `HIGH`, `URGENT`, `CRITICAL`

### Get Resource Status (Admin Only)
```http
GET /queue/status/resources
Authorization: Bearer {admin_token}
```

### Start/Stop Queue (Admin Only)
```http
POST /queue/start
POST /queue/stop
Authorization: Bearer {admin_token}
```

## Analysis Endpoints (Now Require Auth)

### Generate Report with Service Account
```http
POST /generate_report_with_service_account/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "domain_property": "https://example.com/",
  "ga_property_id": "*********",
  "service_account_data": {...},
  "homepage": "https://example.com",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

**Response:**
```json
{
  "task_id": "uuid",
  "status": "queued",
  "message": "Analysis task queued successfully",
  "estimated_duration": 300
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "detail": "You don't have permission to access this resource"
}
```

### 404 Not Found
```json
{
  "detail": "Task not found"
}
```

## Rate Limits & Concurrency

- **Global Limit:** 5 concurrent tasks
- **Per-User Limit:** 3 concurrent tasks
- **Queue Size:** Up to 100 queued tasks
- **Token Expiry:** 30 minutes (access), 7 days (refresh)

## Migration & Backward Compatibility

### Default Admin Account
- **Email:** `<EMAIL>`
- **Password:** `AdminPassword123!`
- **Role:** `admin`

### Existing Data
- All existing tasks assigned to default site
- All existing sites assigned to admin user
- Original endpoints preserved but now require authentication

## WebSocket Support (Future)
Real-time task updates will be available via WebSocket connections:
```
ws://localhost:8000/ws/tasks/{user_id}
```

## Pagination
All list endpoints support pagination:
- `limit`: Number of items (default: 50, max: 100)
- `offset`: Number of items to skip (default: 0)

## Filtering
Task endpoints support filtering:
- `status`: Filter by task status
- `site_id`: Filter by site ID
- `task_type`: Filter by task type

## Security Notes
- All passwords are hashed with bcrypt
- JWT tokens are stored in database sessions
- User data is completely isolated
- Admin users can access all data
- CORS enabled for web applications
