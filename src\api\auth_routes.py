"""
Authentication routes for user registration, login, and profile management
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from src.models.schemas import (
    UserRegistrationSchema, UserLoginSchema, TokenResponse, RefreshTokenSchema,
    UserProfileSchema, UserUpdateSchema, PasswordChangeSchema,
    PasswordResetRequestSchema, PasswordResetSchema
)
from src.services.auth_service import (
    auth_service, AuthenticationError, InvalidCredentialsError, 
    UserNotFoundError, UserAlreadyExistsError, TokenExpiredError, InvalidTokenError
)
from src.utils.logging import get_logger

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/auth", tags=["authentication"])

# Security scheme
security = HTTPBearer()


# ============================================================================
# AUTHENTICATION ENDPOINTS
# ============================================================================

@router.post("/register", response_model=Dict[str, Any])
async def register_user(user_data: UserRegistrationSchema):
    """
    Register a new user
    """
    try:
        result = await auth_service.register_user(
            email=user_data.email,
            username=user_data.username,
            password=user_data.password,
            first_name=user_data.first_name,
            last_name=user_data.last_name
        )
        
        return {
            "success": True,
            "message": result["message"],
            "user_id": result["user_id"]
        }
        
    except UserAlreadyExistsError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login_user(user_credentials: UserLoginSchema, request: Request):
    """
    Login user and return access tokens
    """
    try:
        # Get client IP and user agent
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        result = await auth_service.login_user(
            email_or_username=user_credentials.email_or_username,
            password=user_credentials.password,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        return TokenResponse(**result)
        
    except (UserNotFoundError, InvalidCredentialsError) as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=Dict[str, Any])
async def refresh_token(token_data: RefreshTokenSchema):
    """
    Refresh access token using refresh token
    """
    try:
        result = await auth_service.refresh_access_token(token_data.refresh_token)
        return result
        
    except TokenExpiredError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token has expired"
        )
    except InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Logout user by invalidating session
    """
    try:
        success = await auth_service.logout_user(credentials.credentials)
        
        if success:
            return {"message": "Logged out successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Logout failed"
            )
            
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


# ============================================================================
# USER PROFILE ENDPOINTS
# ============================================================================

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency to get current authenticated user
    """
    try:
        user = await auth_service.get_user_by_token(credentials.credentials)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        return user
        
    except Exception as e:
        logger.error(f"Get current user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


@router.get("/me", response_model=UserProfileSchema)
async def get_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Get current user profile
    """
    try:
        # Get additional user data from database
        from src.services.auth_service import auth_service
        
        user_result = auth_service.supabase.table("users").select("*").eq("id", current_user["user_id"]).execute()
        
        if not user_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user = user_result.data[0]
        
        return UserProfileSchema(
            user_id=user["id"],
            email=user["email"],
            username=user["username"],
            first_name=user["first_name"],
            last_name=user["last_name"],
            role=user["role"],
            is_verified=user["is_verified"],
            created_at=user["created_at"],
            last_login=user["last_login"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/me", response_model=Dict[str, Any])
async def update_user_profile(
    profile_data: UserUpdateSchema,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update current user profile
    """
    try:
        from src.services.auth_service import auth_service
        
        # Prepare update data
        update_data = {}
        if profile_data.first_name is not None:
            update_data["first_name"] = profile_data.first_name
        if profile_data.last_name is not None:
            update_data["last_name"] = profile_data.last_name
        if profile_data.email is not None:
            # Validate email format
            if not auth_service.validate_email_format(profile_data.email):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid email format"
                )
            update_data["email"] = profile_data.email.lower()
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data to update"
            )
        
        # Update user
        result = auth_service.supabase.table("users").update(update_data).eq("id", current_user["user_id"]).execute()
        
        if result.data:
            return {"message": "Profile updated successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update profile"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeSchema,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Change user password
    """
    try:
        from src.services.auth_service import auth_service
        
        # Get current user data
        user_result = auth_service.supabase.table("users").select("password_hash").eq("id", current_user["user_id"]).execute()
        
        if not user_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user = user_result.data[0]
        
        # Verify current password
        if not auth_service.verify_password(password_data.current_password, user["password_hash"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Validate new password strength
        is_strong, message = auth_service.validate_password_strength(password_data.new_password)
        if not is_strong:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # Hash new password
        new_password_hash = auth_service.hash_password(password_data.new_password)
        
        # Update password
        result = auth_service.supabase.table("users").update({
            "password_hash": new_password_hash
        }).eq("id", current_user["user_id"]).execute()
        
        if result.data:
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change password"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Change password error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )


# ============================================================================
# UTILITY ENDPOINTS
# ============================================================================

@router.get("/verify-token")
async def verify_token(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Verify if token is valid
    """
    return {
        "valid": True,
        "user_id": current_user["user_id"],
        "email": current_user["email"],
        "role": current_user["role"]
    }
