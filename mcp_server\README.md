# SEO Analytics MCP Server

A Model Context Protocol (MCP) server that provides authenticated access to SEO analytics data stored in Supabase. This server enables AI assistants and other MCP clients to interact with your SEO data through a secure, well-defined API.

## Features

### 🔐 **Authentication & Security**
- JWT-based user authentication
- Row-level security ensuring users only access their own data
- Token validation for every request
- Role-based access control

### 🏢 **Site Management**
- List all sites owned by authenticated user
- Create new sites with configuration
- Update site configuration (GA, GSC, service accounts)
- Delete sites and associated data
- Get detailed site information and statistics

### 📊 **SEO Data Access**
- Retrieve pages data with filtering and pagination
- Access Google Search Console keywords and traffic data
- Get internal links analysis
- Retrieve Google Analytics data
- Search pages by content, title, or URL
- Get monthly traffic trends

### 🔄 **Analysis & Reporting**
- Start new site analysis (full or incremental)
- Monitor task progress and status
- Get detailed task logs
- Generate Excel reports with custom date ranges
- List all user tasks with filtering

### 📤 **Data Export & Utilities**
- Export site data in JSON or CSV formats
- Get comprehensive data summaries
- Clean up old data snapshots
- Validate site configurations
- Get available analysis dates

## Installation

1. **Install Dependencies**
   ```bash
   pip install -r mcp_server/requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   export SUPABASE_URL="your-supabase-url"
   export SUPABASE_KEY="your-supabase-anon-key"
   export JWT_SECRET_KEY="your-jwt-secret-key"
   ```

3. **Configure MCP Client**
   Add the server configuration to your MCP client:
   ```json
   {
     "mcpServers": {
       "seo-analytics": {
         "command": "python",
         "args": ["-m", "mcp_server.server"],
         "env": {
           "SUPABASE_URL": "${SUPABASE_URL}",
           "SUPABASE_KEY": "${SUPABASE_KEY}",
           "JWT_SECRET_KEY": "${JWT_SECRET_KEY}"
         }
       }
     }
   }
   ```

## Usage

### Authentication

All tools require an `auth_token` parameter. Obtain this token by logging in through the main API:

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email_or_username": "your-email", "password": "your-password"}'
```

### Example Tool Calls

#### List User Sites
```json
{
  "tool": "list_user_sites",
  "arguments": {
    "auth_token": "your-jwt-token"
  }
}
```

#### Get Site Pages
```json
{
  "tool": "get_site_pages",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "123",
    "limit": 50,
    "search": "keyword"
  }
}
```

#### Start Site Analysis
```json
{
  "tool": "start_site_analysis",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "123",
    "analysis_type": "incremental"
  }
}
```

#### Export Site Data
```json
{
  "tool": "export_site_data",
  "arguments": {
    "auth_token": "your-jwt-token",
    "site_id": "123",
    "data_types": ["pages", "keywords", "traffic"],
    "format": "json"
  }
}
```

## Available Tools

### Site Management
- `list_user_sites` - List all sites owned by user
- `get_site_details` - Get detailed site information
- `create_site` - Create new site with configuration
- `update_site_config` - Update site configuration
- `delete_site` - Delete site and all data

### SEO Data Retrieval
- `get_site_pages` - Get pages data with filtering
- `get_site_keywords` - Get GSC keywords data
- `get_site_traffic` - Get GSC traffic data
- `get_internal_links` - Get internal links analysis
- `get_analytics_data` - Get Google Analytics data
- `get_monthly_trends` - Get traffic trends over time
- `search_pages` - Advanced page search

### Analysis & Reporting
- `start_site_analysis` - Start new analysis
- `get_task_status` - Check task progress
- `get_task_logs` - Get detailed task logs
- `list_user_tasks` - List all user tasks
- `generate_excel_report` - Create Excel reports

### Data Export & Utilities
- `export_site_data` - Export data in various formats
- `get_data_summary` - Get comprehensive data summary
- `cleanup_old_data` - Remove old snapshots
- `validate_site_config` - Validate configurations
- `get_available_dates` - Get available analysis dates
- `get_site_statistics` - Get cached site statistics

## Security

- All database queries are filtered by `user_id` to ensure data isolation
- JWT tokens are validated on every request
- Sites table has foreign key relationship to users table
- Row-level security policies prevent cross-user data access

## Error Handling

The server provides detailed error messages for:
- Authentication failures
- Invalid parameters
- Site ownership verification
- Database connection issues
- Tool execution errors

## Development

To run the server in development mode:

```bash
python -m mcp_server.server
```

For debugging, set the log level:
```bash
export LOG_LEVEL=DEBUG
python -m mcp_server.server
```

## Architecture

```
MCP Client
    ↓
MCP Server (server.py)
    ↓
Authentication Service
    ↓
Tool Handlers (analysis_tools.py, data_tools.py)
    ↓
Supabase Client
    ↓
Supabase Database
```

## Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include proper authentication checks
4. Update documentation for new tools
5. Test with multiple users to ensure data isolation

## License

This MCP server is part of the SEO Analytics Tool project.
