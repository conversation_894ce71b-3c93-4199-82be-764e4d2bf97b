"""
Data Export and Utility Tools for SEO Analytics MCP Server
"""

import json
import logging
from typing import Any, Dict
from datetime import datetime, timezone

from mcp.types import Call<PERSON><PERSON><PERSON>esult, TextContent, Tool

logger = logging.getLogger(__name__)

def get_data_tools() -> list[Tool]:
    """Get data export and utility tool definitions"""
    return [
        Tool(
            name="export_site_data",
            description="Export site data in various formats (JSON, CSV)",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to export data for"
                    },
                    "data_types": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "enum": ["pages", "keywords", "traffic", "internal_links", "analytics"]
                        },
                        "description": "Types of data to export"
                    },
                    "format": {
                        "type": "string",
                        "enum": ["json", "csv"],
                        "description": "Export format (default: json)"
                    },
                    "date_from": {
                        "type": "string",
                        "description": "Filter by date from (YYYY-MM-DD)"
                    },
                    "date_to": {
                        "type": "string",
                        "description": "Filter by date to (YYYY-MM-DD)"
                    }
                },
                "required": ["auth_token", "site_id", "data_types"]
            }
        ),
        Tool(
            name="get_data_summary",
            description="Get comprehensive data summary for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to get summary for"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="cleanup_old_data",
            description="Remove old snapshots and data for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to cleanup data for"
                    },
                    "keep_days": {
                        "type": "integer",
                        "description": "Number of days of data to keep (default: 90)"
                    },
                    "confirm": {
                        "type": "boolean",
                        "description": "Confirmation flag (must be true)"
                    }
                },
                "required": ["auth_token", "site_id", "confirm"]
            }
        ),
        Tool(
            name="validate_site_config",
            description="Validate site configuration and API credentials",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to validate"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="get_available_dates",
            description="Get available analysis dates for a site",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to get dates for"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        ),
        Tool(
            name="search_pages",
            description="Search pages by content, title, or URL with advanced filtering",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to search in"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "search_fields": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "enum": ["url", "title", "content", "h1", "meta_description"]
                        },
                        "description": "Fields to search in (default: all)"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of results to return (default: 50, max: 200)"
                    },
                    "min_clicks": {
                        "type": "integer",
                        "description": "Minimum GSC clicks filter"
                    },
                    "min_impressions": {
                        "type": "integer",
                        "description": "Minimum GSC impressions filter"
                    }
                },
                "required": ["auth_token", "site_id", "query"]
            }
        ),
        Tool(
            name="get_site_statistics",
            description="Get cached site statistics and performance metrics",
            inputSchema={
                "type": "object",
                "properties": {
                    "auth_token": {
                        "type": "string",
                        "description": "JWT authentication token"
                    },
                    "site_id": {
                        "type": "string",
                        "description": "Site ID to get statistics for"
                    }
                },
                "required": ["auth_token", "site_id"]
            }
        )
    ]


async def handle_data_tool(tool_name: str, user: Dict[str, Any], args: Dict[str, Any], 
                          auth_service, settings) -> CallToolResult:
    """Handle data export and utility tool calls"""
    
    if tool_name == "export_site_data":
        return await _handle_export_site_data(user, args, auth_service, settings)
    elif tool_name == "get_data_summary":
        return await _handle_get_data_summary(user, args, auth_service, settings)
    elif tool_name == "cleanup_old_data":
        return await _handle_cleanup_old_data(user, args, auth_service, settings)
    elif tool_name == "validate_site_config":
        return await _handle_validate_site_config(user, args, auth_service, settings)
    elif tool_name == "get_available_dates":
        return await _handle_get_available_dates(user, args, auth_service, settings)
    elif tool_name == "search_pages":
        return await _handle_search_pages(user, args, auth_service, settings)
    elif tool_name == "get_site_statistics":
        return await _handle_get_site_statistics(user, args, auth_service, settings)
    else:
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: Unknown data tool '{tool_name}'")]
        )


async def _verify_site_ownership(user: Dict[str, Any], site_id: str, settings) -> bool:
    """Verify that user owns the specified site"""
    try:
        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )
        result = client.supabase.table("sites").select("id").eq("id", site_id).eq("user_id", user["user_id"]).execute()
        return len(result.data) > 0
    except Exception as e:
        logger.error(f"Error verifying site ownership: {e}")
        return False


async def _handle_export_site_data(user: Dict[str, Any], args: Dict[str, Any], 
                                 auth_service, settings) -> CallToolResult:
    """Handle export_site_data tool call"""
    try:
        site_id = args.get("site_id")
        data_types = args.get("data_types", [])
        export_format = args.get("format", "json")
        
        if not site_id or not data_types:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id and data_types are required")]
            )
        
        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )
        
        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )
        
        exported_data = {}
        
        # Export each requested data type
        for data_type in data_types:
            if data_type == "pages":
                query = client.supabase.table("pages").select("*").eq("site_id", site_id)
                if args.get("date_from"):
                    query = query.gte("snapshot_date", args["date_from"])
                if args.get("date_to"):
                    query = query.lte("snapshot_date", args["date_to"])
                result = query.execute()
                exported_data["pages"] = result.data
                
            elif data_type == "keywords":
                query = client.supabase.table("gsc_keywords").select("*").eq("site_id", site_id)
                result = query.execute()
                exported_data["keywords"] = result.data
                
            elif data_type == "traffic":
                query = client.supabase.table("gsc_traffic").select("*").eq("site_id", site_id)
                result = query.execute()
                exported_data["traffic"] = result.data
                
            elif data_type == "internal_links":
                query = client.supabase.table("internal_links").select("*").eq("site_id", site_id)
                if args.get("date_from"):
                    query = query.gte("snapshot_date", args["date_from"])
                if args.get("date_to"):
                    query = query.lte("snapshot_date", args["date_to"])
                result = query.execute()
                exported_data["internal_links"] = result.data
                
            elif data_type == "analytics":
                query = client.supabase.table("ga_data").select("*").eq("site_id", site_id)
                result = query.execute()
                exported_data["analytics"] = result.data
        
        # Get site info
        site_result = client.supabase.table("sites").select("domain").eq("id", site_id).execute()
        domain = site_result.data[0]["domain"] if site_result.data else "unknown"
        
        response = {
            "success": True,
            "site_id": site_id,
            "domain": domain,
            "export_format": export_format,
            "exported_types": data_types,
            "export_timestamp": datetime.now(timezone.utc).isoformat(),
            "data": exported_data,
            "record_counts": {data_type: len(exported_data.get(data_type, [])) for data_type in data_types}
        }
        
        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )
        
    except Exception as e:
        logger.error(f"Error exporting site data: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_get_data_summary(user: Dict[str, Any], args: Dict[str, Any],
                                 auth_service, settings) -> CallToolResult:
    """Handle get_data_summary tool call"""
    try:
        site_id = args.get("site_id")
        if not site_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id is required")]
            )

        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Get site info
        site_result = client.supabase.table("sites").select("*").eq("id", site_id).execute()
        if not site_result.data:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found")]
            )

        site = site_result.data[0]

        # Get data counts
        pages_count = client.supabase.table("pages").select("id", count="exact").eq("site_id", site_id).execute().count
        keywords_count = client.supabase.table("gsc_keywords").select("id", count="exact").eq("site_id", site_id).execute().count
        traffic_count = client.supabase.table("gsc_traffic").select("id", count="exact").eq("site_id", site_id).execute().count
        links_count = client.supabase.table("internal_links").select("id", count="exact").eq("site_id", site_id).execute().count
        analytics_count = client.supabase.table("ga_data").select("id", count="exact").eq("site_id", site_id).execute().count

        response = {
            "site_id": site_id,
            "domain": site["domain"],
            "created_at": site["created_at"],
            "last_updated": site.get("last_updated"),
            "last_analysis_date": site.get("last_analysis_date"),
            "data_counts": {
                "pages": pages_count,
                "keywords": keywords_count,
                "traffic_records": traffic_count,
                "internal_links": links_count,
                "analytics_records": analytics_count,
                "total_records": pages_count + keywords_count + traffic_count + links_count + analytics_count
            },
            "configuration": {
                "domain_property": site.get("domain_property"),
                "ga_property_id": site.get("ga_property_id"),
                "homepage": site.get("homepage"),
                "has_wp_api_key": bool(site.get("wp_api_key")),
                "has_service_account": bool(site.get("service_account_data"))
            }
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error getting data summary: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


# Additional tool handlers would be implemented here
# For brevity, I'm including placeholders for the remaining tools

async def _handle_cleanup_old_data(user: Dict[str, Any], args: Dict[str, Any],
                                 auth_service, settings) -> CallToolResult:
    """Handle cleanup_old_data tool call"""
    return CallToolResult(
        content=[TextContent(type="text", text="Cleanup functionality not yet implemented")]
    )


async def _handle_validate_site_config(user: Dict[str, Any], args: Dict[str, Any],
                                     auth_service, settings) -> CallToolResult:
    """Handle validate_site_config tool call"""
    return CallToolResult(
        content=[TextContent(type="text", text="Validation functionality not yet implemented")]
    )


async def _handle_get_available_dates(user: Dict[str, Any], args: Dict[str, Any],
                                    auth_service, settings) -> CallToolResult:
    """Handle get_available_dates tool call"""
    try:
        site_id = args.get("site_id")
        if not site_id:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: site_id is required")]
            )

        # Verify ownership
        if not await _verify_site_ownership(user, site_id, settings):
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Site not found or access denied")]
            )

        from src.database.supabase_client import SupabaseClient
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="",
            user_id=user["user_id"]
        )

        # Get available dates from pages table
        pages_dates = client.supabase.table("pages").select("snapshot_date").eq("site_id", site_id).execute()
        available_dates = list(set([str(row["snapshot_date"]) for row in pages_dates.data]))
        available_dates.sort()

        # Get available months from keywords table
        keywords_months = client.supabase.table("gsc_keywords").select("Month").eq("site_id", site_id).execute()
        available_months = list(set([row["Month"] for row in keywords_months.data]))
        available_months.sort()

        response = {
            "site_id": site_id,
            "available_dates": available_dates,
            "available_months": available_months,
            "date_count": len(available_dates),
            "month_count": len(available_months)
        }

        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(response, indent=2, default=str))]
        )

    except Exception as e:
        logger.error(f"Error getting available dates: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def _handle_search_pages(user: Dict[str, Any], args: Dict[str, Any],
                             auth_service, settings) -> CallToolResult:
    """Handle search_pages tool call"""
    return CallToolResult(
        content=[TextContent(type="text", text="Search functionality not yet implemented")]
    )


async def _handle_get_site_statistics(user: Dict[str, Any], args: Dict[str, Any],
                                    auth_service, settings) -> CallToolResult:
    """Handle get_site_statistics tool call"""
    return CallToolResult(
        content=[TextContent(type="text", text="Statistics functionality not yet implemented")]
    )
